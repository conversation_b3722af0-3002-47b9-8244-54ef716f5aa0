# Lifeboard Code Quality Makefile
#
# This Makefile provides convenient targets for code quality operations
# including linting, formatting, testing, and code smell detection.
#
# Usage:
#   make help           - Show this help message
#   make lint           - Run all linters
#   make fix            - Auto-fix code issues where possible
#   make test           - Run all tests
#   make smell          - Run code smell detection
#   make quality        - Run comprehensive quality checks
#   make setup          - Set up development environment

.PHONY: help lint fix test smell quality setup clean pre-commit
.DEFAULT_GOAL := help

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# Project directories
PROJECT_ROOT := $(shell pwd)
TOOLS_DIR := $(PROJECT_ROOT)/tools
LOGS_DIR := $(PROJECT_ROOT)/logs
DESKTOP_DIR := $(PROJECT_ROOT)/desktop

help: ## Show this help message
	@echo "$(BLUE)Lifeboard Code Quality Commands$(RESET)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | \
		awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-15s$(RESET) %s\n", $$1, $$2}'

setup: ## Set up development environment
	@echo "$(BLUE)Setting up development environment...$(RESET)"
	@python3 -m pip install --upgrade pip
	@pip install pre-commit pylint bandit black isort flake8 sqlfluff yamllint
	@npm install -g eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin markdownlint-cli2
	@if command -v brew >/dev/null 2>&1; then \
		brew install shellcheck hadolint golangci-lint; \
	else \
		echo "$(YELLOW)Please install shellcheck, hadolint, and golangci-lint manually$(RESET)"; \
	fi
	@pre-commit install
	@mkdir -p $(LOGS_DIR)
	@echo "$(YELLOW)Bootstrapping log directory structure...$(RESET)"
	@node $(PROJECT_ROOT)/scripts/bootstrap_logs.js || \
		(echo "$(RED)Log directory bootstrap failed$(RESET)" && exit 1)
	@echo "$(GREEN)Development environment setup complete!$(RESET)"

lint: ## Run all linters
	@echo "$(BLUE)Running comprehensive linting...$(RESET)"
	@$(MAKE) lint-shell
	@$(MAKE) lint-python
	@$(MAKE) lint-js
	@$(MAKE) lint-go
	@$(MAKE) lint-sql
	@$(MAKE) lint-yaml
	@$(MAKE) lint-docker
	@$(MAKE) lint-markdown
	@echo "$(GREEN)All linting completed!$(RESET)"

lint-shell: ## Run ShellCheck on shell scripts
	@echo "$(YELLOW)Linting shell scripts...$(RESET)"
	@find . -name "*.sh" -not -path "./node_modules/*" -not -path "./volumes/*" | \
		xargs shellcheck -f gcc || echo "$(RED)ShellCheck issues found$(RESET)"

lint-python: ## Run Python linters (pylint, flake8, bandit)
	@echo "$(YELLOW)Linting Python code...$(RESET)"
	@find . -name "*.py" -not -path "./node_modules/*" -not -path "./volumes/*" | \
		xargs pylint --rcfile=.pylintrc || echo "$(RED)Pylint issues found$(RESET)"
	@flake8 . --config=.flake8 || echo "$(RED)Flake8 issues found$(RESET)"
	@bandit -r . -c .bandit || echo "$(RED)Bandit security issues found$(RESET)"

lint-js: ## Run ESLint on JavaScript/TypeScript
	@echo "$(YELLOW)Linting JavaScript/TypeScript...$(RESET)"
	@if [ -d "$(DESKTOP_DIR)" ]; then \
		cd $(DESKTOP_DIR) && npm run lint; \
	fi
	@npx eslint . --ext .js,.jsx,.ts,.tsx || echo "$(RED)ESLint issues found$(RESET)"

lint-go: ## Run golangci-lint on Go code
	@echo "$(YELLOW)Linting Go code...$(RESET)"
	@if ls *.go 1> /dev/null 2>&1; then \
		golangci-lint run --config=.golangci.yml; \
	else \
		echo "No Go files found"; \
	fi

lint-sql: ## Run SQLFluff on SQL files
	@echo "$(YELLOW)Linting SQL files...$(RESET)"
	@if ls **/*.sql 1> /dev/null 2>&1; then \
		sqlfluff lint . --dialect postgres --config=.sqlfluff; \
	else \
		echo "No SQL files found"; \
	fi

lint-yaml: ## Run yamllint on YAML files
	@echo "$(YELLOW)Linting YAML files...$(RESET)"
	@yamllint . -c .yamllint.yml || echo "$(RED)YAML lint issues found$(RESET)"

lint-docker: ## Run Hadolint on Docker files
	@echo "$(YELLOW)Linting Docker files...$(RESET)"
	@find . -name "Dockerfile*" -o -name "*.dockerfile" | \
		xargs -I {} hadolint {} --config=.hadolint.yaml || echo "$(RED)Docker lint issues found$(RESET)"

lint-markdown: ## Run markdownlint on Markdown files
	@echo "$(YELLOW)Linting Markdown files...$(RESET)"
	@markdownlint-cli2 "**/*.md" --config=.markdownlint.yml || echo "$(RED)Markdown issues found$(RESET)"

fix: ## Auto-fix code issues where possible
	@echo "$(BLUE)Auto-fixing code issues...$(RESET)"
	@$(MAKE) fix-python
	@$(MAKE) fix-js
	@$(MAKE) fix-sql
	@echo "$(GREEN)Auto-fix completed!$(RESET)"

fix-python: ## Auto-fix Python code formatting
	@echo "$(YELLOW)Fixing Python formatting...$(RESET)"
	@black .
	@isort . --profile=black

fix-js: ## Auto-fix JavaScript/TypeScript formatting
	@echo "$(YELLOW)Fixing JavaScript/TypeScript formatting...$(RESET)"
	@npx eslint . --ext .js,.jsx,.ts,.tsx --fix
	@if [ -d "$(DESKTOP_DIR)" ]; then \
		cd $(DESKTOP_DIR) && npm run lint:fix; \
	fi

fix-sql: ## Auto-fix SQL formatting
	@echo "$(YELLOW)Fixing SQL formatting...$(RESET)"
	@if ls **/*.sql 1> /dev/null 2>&1; then \
		sqlfluff fix . --dialect postgres --config=.sqlfluff; \
	else \
		echo "No SQL files found"; \
	fi

smell: ## Run comprehensive code smell detection
	@echo "$(BLUE)Running code smell detection...$(RESET)"
	@python3 $(TOOLS_DIR)/code_smell_detector.py \
		--project . \
		--output $(LOGS_DIR)/code-smell-report.md \
		--format markdown
	@echo "$(GREEN)Code smell analysis complete. Report saved to $(LOGS_DIR)/code-smell-report.md$(RESET)"

smell-json: ## Run code smell detection with JSON output
	@echo "$(BLUE)Running code smell detection (JSON output)...$(RESET)"
	@python3 $(TOOLS_DIR)/code_smell_detector.py \
		--project . \
		--output $(LOGS_DIR)/code-smell-report.json \
		--format json
	@echo "$(GREEN)Code smell analysis complete. Report saved to $(LOGS_DIR)/code-smell-report.json$(RESET)"

smell-baseline: ## Save current state as baseline
	@echo "$(BLUE)Saving code smell baseline...$(RESET)"
	@python3 $(TOOLS_DIR)/code_smell_detector.py \
		--project . \
		--baseline \
		--format json
	@echo "$(GREEN)Baseline saved successfully$(RESET)"

quality: ## Run comprehensive quality checks
	@echo "$(BLUE)Running comprehensive quality checks...$(RESET)"
	@$(MAKE) lint
	@$(MAKE) test
	@$(MAKE) smell
	@echo "$(GREEN)Quality checks completed!$(RESET)"

test: ## Run all tests
	@echo "$(BLUE)Running all tests...$(RESET)"
	@./tests/run_all_tests.sh
	@echo "$(GREEN)All tests completed!$(RESET)"

test-health: ## Run health check tests
	@echo "$(YELLOW)Running health check tests...$(RESET)"
	@./tests/test_health_checks.sh

test-security: ## Run security tests
	@echo "$(YELLOW)Running security tests...$(RESET)"
	@./tests/test_security_scan.sh

test-integration: ## Run integration tests
	@echo "$(YELLOW)Running integration tests...$(RESET)"
	@./tests/test_crud_integration.sh

pre-commit: ## Run pre-commit hooks on all files
	@echo "$(BLUE)Running pre-commit hooks...$(RESET)"
	@pre-commit run --all-files
	@echo "$(GREEN)Pre-commit hooks completed!$(RESET)"

clean: ## Clean up generated files and logs
	@echo "$(BLUE)Cleaning up...$(RESET)"
	@rm -rf $(LOGS_DIR)/*.log
	@rm -rf $(LOGS_DIR)/*.json
	@rm -rf $(LOGS_DIR)/*.md
	@find . -name "*.pyc" -delete
	@find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "$(GREEN)Cleanup completed!$(RESET)"

deploy-dev: ## Deploy development environment
	@echo "$(BLUE)Deploying development environment...$(RESET)"
	@./scripts/deploy.sh --profile base --detach
	@echo "$(GREEN)Development environment deployed!$(RESET)"

deploy-studio: ## Deploy with Supabase Studio
	@echo "$(BLUE)Deploying with Supabase Studio...$(RESET)"
	@./scripts/deploy.sh --profile studio
	@echo "$(GREEN)Environment with Studio deployed!$(RESET)"

logs: ## View recent logs
	@echo "$(BLUE)Recent log files:$(RESET)"
	@ls -la $(LOGS_DIR)/ | head -20

docs-quality: ## Generate quality documentation
	@echo "$(BLUE)Generating quality documentation...$(RESET)"
	@python3 $(TOOLS_DIR)/code_smell_detector.py \
		--project . \
		--output supporting_documents/Code_Quality_Report.md \
		--format markdown
	@echo "$(GREEN)Quality documentation generated in supporting_documents/$(RESET)"

# Development convenience targets
dev-setup: setup deploy-dev ## Set up and deploy development environment

ci-test: ## Run CI-style tests locally
	@echo "$(BLUE)Running CI-style tests locally...$(RESET)"
	@$(MAKE) pre-commit
	@$(MAKE) quality
	@echo "$(GREEN)CI-style tests completed!$(RESET)"

# Quality gate enforcement
quality-gates: ## Check quality gates
	@echo "$(BLUE)Checking quality gates...$(RESET)"
	@python3 $(TOOLS_DIR)/code_smell_detector.py \
		--project . \
		--output /tmp/quality-gates.json \
		--format json
	@python3 -c "import json; import sys; \
		result = json.load(open('/tmp/quality-gates.json')); \
		failed = [name for name, passed in result['quality_gates'].items() if not passed]; \
		print('Quality gates:', 'PASSED' if not failed else f'FAILED: {failed}'); \
		sys.exit(0 if not failed else 1)"

# Information targets
info: ## Show project information
	@echo "$(BLUE)Lifeboard Project Information$(RESET)"
	@echo "Project Root: $(PROJECT_ROOT)"
	@echo "Tools Dir: $(TOOLS_DIR)"
	@echo "Logs Dir: $(LOGS_DIR)"
	@echo ""
	@echo "$(YELLOW)Available Quality Tools:$(RESET)"
	@command -v shellcheck >/dev/null && echo "✓ ShellCheck" || echo "✗ ShellCheck"
	@command -v pylint >/dev/null && echo "✓ Pylint" || echo "✗ Pylint"
	@command -v eslint >/dev/null && echo "✓ ESLint" || echo "✗ ESLint"
	@command -v golangci-lint >/dev/null && echo "✓ GolangCI-Lint" || echo "✗ GolangCI-Lint"
	@command -v sqlfluff >/dev/null && echo "✓ SQLFluff" || echo "✗ SQLFluff"
	@command -v yamllint >/dev/null && echo "✓ YAMLLint" || echo "✗ YAMLLint"
	@command -v hadolint >/dev/null && echo "✓ Hadolint" || echo "✗ Hadolint"
	@command -v markdownlint-cli2 >/dev/null && echo "✓ MarkdownLint" || echo "✗ MarkdownLint"

watch: ## Watch for changes and run quality checks
	@echo "$(BLUE)Watching for changes...$(RESET)"
	@echo "$(YELLOW)Press Ctrl+C to stop$(RESET)"
	@while true; do \
		inotifywait -r -e modify,create,delete . --exclude='(logs|volumes|node_modules|\.git)' 2>/dev/null; \
		echo "$(YELLOW)Changes detected, running quality checks...$(RESET)"; \
		$(MAKE) lint; \
		sleep 2; \
	done
