/**
 * Frontend Logger for Lifeboard WebUI
 * Provides structured logging with IPC channel communication to backend
 *
 * Features:
 * - Structured logging with correlation IDs
 * - User action logging helpers
 * - Performance timing utilities
 * - Error context capture with stack traces
 * - Debug context management
 * - Performance metrics and tracing
 * - Fallback to console logging
 * - Sensitive data sanitization
 * - Batch logging capabilities
 * - Function call tracing
 *
 * @version 2.0.0
 */

class FrontendLogger {
    constructor() {
        this.isElectron = typeof window !== 'undefined' && window.lifeboard && window.lifeboard.log;
        this.batchQueue = [];
        this.batchTimeout = null;
        this.batchSize = 10;
        this.batchTimeoutMs = 1000;
        this.sessionId = this.generateSessionId();
        this.correlationId = this.generateCorrelationId();
        this.storageKey = 'lifeboard_logs';
        this.maxLogs = 1000;
        this.maxStorageSize = 5 * 1024 * 1024; // 5MB
        this.storageWarningShown = false;
        this.debugContexts = new Map();
        this.debugEnabled = true;

        // Performance tracking
        this.timings = new Map();
        this.performanceMetrics = {
            logCount: 0,
            errorCount: 0,
            warnCount: 0,
            lastError: null,
            lastWarning: null,
            logRate: 0,
            lastLogTime: Date.now()
        };

        // Sensitive data patterns for sanitization
        this.sensitivePatterns = [
            /password/i,
            /token/i,
            /apikey/i,
            /secret/i,
            /auth/i,
            /credential/i,
            /key/i
        ];

        this.init();
    }

    /**
     * Initialize the logger
     */
    init() {
        if (this.isElectron) {
            this.info('Frontend logger initialized', {
                sessionId: this.sessionId,
                correlationId: this.correlationId,
                batchSize: this.batchSize,
                batchTimeoutMs: this.batchTimeoutMs
            });
        } else {
            console.log('Frontend logger initialized (console fallback mode)');
        }

        // Setup performance monitoring
        this.setupPerformanceMonitoring();

        // Setup error handlers
        this.setupErrorHandlers();

        // Flush any persisted logs from previous session
        this.flushPersistedLogs();

        // Setup beforeunload handler to ensure logs are persisted
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => this.persistBatchQueue());
        }
    }

    /**
     * Generate unique session ID
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate correlation ID for request tracing
     */
    generateCorrelationId() {
        return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Sanitize sensitive data from objects
     */
    sanitizeData(data) {
        if (!data || typeof data !== 'object') {
            return data;
        }

        const sanitized = JSON.parse(JSON.stringify(data));

        const sanitizeRecursive = (obj) => {
            if (Array.isArray(obj)) {
                return obj.map(item => sanitizeRecursive(item));
            }

            if (obj && typeof obj === 'object') {
                const sanitizedObj = {};
                for (const [key, value] of Object.entries(obj)) {
                    if (this.sensitivePatterns.some(pattern => pattern.test(key))) {
                        sanitizedObj[key] = '[REDACTED]';
                    } else {
                        sanitizedObj[key] = sanitizeRecursive(value);
                    }
                }
                return sanitizedObj;
            }

            return obj;
        };

        return sanitizeRecursive(sanitized);
    }

    /**
     * Get current call stack (excluding logger frames)
     */
    getCallStack(depth = 5) {
        try {
            const stack = new Error().stack.split('\n');
            // Remove logger frames from the top of the stack
            const relevantStack = stack
                .filter(line => !line.includes('Logger.') && !line.includes('logger.js'))
                .slice(0, depth);
            return relevantStack.map(line => line.trim()).join('\n');
        } catch (e) {
            return 'Unable to get call stack';
        }
    }

    /**
     * Format function arguments for logging
     */
    formatArguments(args) {
        if (!args || args.length === 0) return '()';
        
        return `(${Array.from(args).map(arg => {
            try {
                if (arg === undefined) return 'undefined';
                if (arg === null) return 'null';
                if (typeof arg === 'function') return '[Function]';
                if (arg instanceof Error) return `Error: ${arg.message}\n${arg.stack}`;
                return JSON.stringify(arg, this.getCircularReplacer(), 2);
            } catch (e) {
                return `[Error stringifying: ${e.message}]`;
            }
        }).join(', ')})`;
    }

    /**
     * Helper to handle circular references in JSON
     */
    getCircularReplacer() {
        const seen = new WeakSet();
        return (key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) return '[Circular]';
                seen.add(value);
            }
            return value;
        };
    }

    /**
     * Create structured log entry with enhanced debugging info
     */
    createLogEntry(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const entry = {
            timestamp,
            level: level.toUpperCase(),
            component: meta.component || 'webui',
            sessionId: this.sessionId,
            correlationId: meta.correlationId || this.correlationId,
            message: String(message),
            meta: this.sanitizeData(meta),
            debug: {
                callStack: this.getCallStack(5),
                timestampNs: performance?.now() * 1000 || Date.now() * 1000,
                environment: {
                    url: typeof window !== 'undefined' ? window.location.href : '',
                    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
                    platform: typeof navigator !== 'undefined' ? navigator.platform : '',
                    language: typeof navigator !== 'undefined' ? navigator.language : '',
                    online: typeof navigator !== 'undefined' ? navigator.onLine : false
                }
            }
        };

        // Add debug context if available
        if (meta.debugContextId && this.debugContexts.has(meta.debugContextId)) {
            entry.debug.context = this.debugContexts.get(meta.debugContextId);
        }

        return entry;
    }

    /**
     * Create a debug context for tracking operations
     */
    createDebugContext(name, context = {}) {
        const ctx = {
            id: `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
            name,
            startTime: performance?.now() || Date.now(),
            context: { ...context },
            debug: (msg, data = {}) => {
                const now = performance?.now() || Date.now();
                this.debug(`${name}: ${msg}`, {
                    ...data,
                    _ctx: { id: ctx.id, name, duration: now - ctx.startTime }
                });
            }
        };
        
        this.debugContexts.set(ctx.id, ctx);
        
        // Auto-cleanup after 1 hour
        setTimeout(() => {
            this.debugContexts.delete(ctx.id);
        }, 60 * 60 * 1000);
        
        return ctx;
    }

    /**
     * Execute a function with debug context
     */
    async withDebugContext(name, fn, context = {}) {
        const ctx = this.createDebugContext(name, context);
        try {
            const result = await fn(ctx);
            return result;
        } finally {
            this.debugContexts.delete(ctx.id);
        }
    }

    /**
     * Add performance tracking to a function
     */
    withPerformanceTracking(fn, name = fn.name) {
        return async (...args) => {
            const ctx = this.createDebugContext(`perf:${name}`, { args: this.sanitizeData(args) });
            const start = performance?.now() || Date.now();
            
            try {
                const result = await fn(...args);
                const end = performance?.now() || Date.now();
                ctx.debug(`Completed in ${(end - start).toFixed(2)}ms`, { result });
                return result;
            } catch (error) {
                const end = performance?.now() || Date.now();
                ctx.debug(`Failed after ${(end - start).toFixed(2)}ms`, { error });
                throw error;
            } finally {
                this.debugContexts.delete(ctx.id);
            }
        };
    }

    /**
     * Send log to backend via IPC or console fallback
     */
    async sendLog(level, message, meta = {}) {
        // Update metrics
        this.performanceMetrics.logCount++;
        if (level === 'error') this.performanceMetrics.lastError = { message, meta };
        if (level === 'warn') this.performanceMetrics.lastWarning = { message, meta };
        
        const now = Date.now();
        const timeSinceLastLog = now - this.performanceMetrics.lastLogTime;
        this.performanceMetrics.logRate = 1000 / Math.max(timeSinceLastLog, 1);
        this.performanceMetrics.lastLogTime = now;

        const logEntry = this.createLogEntry(level, message, meta);

        if (this.isElectron) {
            try {
                await window.lifeboard.log[level.toLowerCase()](logEntry);
            } catch (error) {
                console.error('Failed to send log via IPC:', error);
                this.fallbackToConsole(level, message, meta);
            }
        } else {
            this.fallbackToConsole(level, message, meta);
        }
        
        return logEntry;
    }

    /**
     * Enhanced error logging with full context
     */
    error(message, error, meta = {}) {
        const errorInfo = error instanceof Error 
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
                ...(error.cause && { cause: error.cause }),
                ...(error.code && { code: error.code })
            }
            : { message: String(error) };

        return this.sendLog('error', message, {
            ...meta,
            error: errorInfo,
            debugContext: this.getCurrentDebugContext()
        });
    }

    /**
     * Get current debug context from async storage
     */
    getCurrentDebugContext() {
        // In a real implementation, this would use AsyncLocalStorage or similar
        return typeof window !== 'undefined' 
            ? window.__currentDebugContext 
            : null;
    }

    /**
     * Fallback to console logging with enhanced formatting
     */
    fallbackToConsole(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const levelStr = level.toUpperCase().padEnd(5);
        const logMessage = `[${timestamp}] ${levelStr} ${message}`;
        
        // Create a styled message for better readability
        const styles = {
            error: 'color: #ff6b6b; font-weight: bold;',
            warn: 'color: #ffd93d;',
            info: 'color: #4d96ff;',
            debug: 'color: #6c757d;',
            default: 'color: inherit;'
        };
        
        const style = styles[level] || styles.default;
        
        // Group related logs together
        if (meta._group) {
            console.groupCollapsed(`%c${logMessage}`, style);
        } else {
            console.log(`%c${logMessage}`, style);
        }
        
        // Log additional data if present
        if (Object.keys(meta).length > 0) {
            // Don't log internal debug fields
            const { _group, _time, ...loggableMeta } = meta;
            if (Object.keys(loggableMeta).length > 0) {
                console.log(loggableMeta);
            }
        }
        
        if (meta._group) {
            console.groupEnd();
        }
    }

    /**
     * Add log to batch queue
     */
    addToBatch(level, message, meta = {}) {
        const logEntry = this.createLogEntry(level, message, meta);
        this.batchQueue.push({ level, entry: logEntry });

        if (this.batchQueue.length >= this.batchSize) {
            this.flushBatch();
        } else if (!this.batchTimeout) {
            this.batchTimeout = setTimeout(() => {
                this.flushBatch();
            }, this.batchTimeoutMs);
        }

        // Persist the batch queue to survive page refreshes
        this.persistBatchQueue();
    }

    /**
     * Flush batch queue
     */
    async flushBatch() {
        if (this.batchQueue.length === 0) return;

        const batch = [...this.batchQueue];
        this.batchQueue = [];

        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }

        if (this.isElectron) {
            try {
                await window.lifeboard.log.info({
                    message: 'Batch log entries',
                    batch: batch.map(item => item.entry),
                    batchSize: batch.length
                });
            } catch (error) {
                console.error('Failed to send batch logs:', error);
                batch.forEach(({ level, entry }) => {
                    this.fallbackToConsole(level, entry.message, entry.meta);
                });
            }
        } else {
            batch.forEach(({ level, entry }) => {
                this.fallbackToConsole(level, entry.message, entry.meta);
            });
        }
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Page load performance
        if (window.performance && window.performance.timing) {
            window.addEventListener('load', () => {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;

                this.info('Page load performance', {
                    type: 'performance',
                    loadTime: loadTime,
                    domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                    firstPaint: timing.responseStart - timing.navigationStart,
                    url: window.location.href
                });
            });
        }

        // Resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.error('Resource loading error', {
                    type: 'resource_error',
                    source: event.target.src || event.target.href,
                    tagName: event.target.tagName,
                    message: event.message
                });
            }
        });
    }

    /**
     * Setup error handlers
     */
    setupErrorHandlers() {
        // Global error handler
        window.addEventListener('error', (event) => {
            if (event.target === window) {
                this.error('JavaScript error', {
                    type: 'javascript_error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                });
            }
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.error('Unhandled promise rejection', {
                type: 'unhandled_rejection',
                reason: event.reason?.toString() || 'Unknown reason',
                stack: event.reason?.stack
            });
        });
    }

    /**
     * Start performance timing
     */
    startTiming(name) {
        this.timings.set(name, {
            startTime: performance.now(),
            endTime: null
        });
    }

    /**
     * End performance timing and log result
     */
    endTiming(name, meta = {}) {
        const timing = this.timings.get(name);
        if (timing) {
            timing.endTime = performance.now();
            const duration = timing.endTime - timing.startTime;

            this.info(`Performance timing: ${name}`, {
                type: 'performance_timing',
                name: name,
                duration: duration,
                startTime: timing.startTime,
                endTime: timing.endTime,
                ...meta
            });

            this.timings.delete(name);
            return duration;
        }

        this.warn(`No timing found for: ${name}`);
        return null;
    }

    /**
     * Log user action
     */
    logUserAction(action, element, meta = {}) {
        const elementInfo = {
            tagName: element?.tagName,
            id: element?.id,
            className: element?.className,
            textContent: element?.textContent?.substring(0, 100) // Limit text content
        };

        this.info(`User action: ${action}`, {
            type: 'user_action',
            action: action,
            element: elementInfo,
            timestamp: new Date().toISOString(),
            ...meta
        });
    }

    /**
     * Log form submission
     */
    logFormSubmission(formElement, formData, meta = {}) {
        const sanitizedData = this.sanitizeData(formData);

        this.info('Form submission', {
            type: 'form_submission',
            formId: formElement.id,
            formName: formElement.name,
            formData: sanitizedData,
            fieldCount: Object.keys(formData).length,
            ...meta
        });
    }

    /**
     * Log navigation event
     */
    logNavigation(from, to, meta = {}) {
        this.info('Navigation event', {
            type: 'navigation',
            from: from,
            to: to,
            timestamp: new Date().toISOString(),
            ...meta
        });
    }

    /**
     * Log component lifecycle
     */
    logComponentLifecycle(component, event, meta = {}) {
        this.debug(`Component lifecycle: ${component} - ${event}`, {
            type: 'component_lifecycle',
            component: component,
            event: event,
            timestamp: new Date().toISOString(),
            ...meta
        });
    }

    /**
     * Log API call
     */
    logApiCall(method, url, duration, status, meta = {}) {
        const level = status >= 400 ? 'error' : 'info';

        this[level](`API call: ${method} ${url}`, {
            type: 'api_call',
            method: method,
            url: url,
            duration: duration,
            status: status,
            timestamp: new Date().toISOString(),
            ...meta
        });
    }

    // Core logging methods
    debug(message, meta = {}) {
        this.sendLog('debug', message, meta);
    }

    info(message, meta = {}) {
        this.sendLog('info', message, meta);
    }

    warn(message, meta = {}) {
        this.sendLog('warn', message, meta);
    }

    error(message, meta = {}) {
        this.sendLog('error', message, meta);
    }

    // Batch logging methods
    batchDebug(message, meta = {}) {
        this.addToBatch('debug', message, meta);
    }

    batchInfo(message, meta = {}) {
        this.addToBatch('info', message, meta);
    }

    batchWarn(message, meta = {}) {
        this.addToBatch('warn', message, meta);
    }

    batchError(message, meta = {}) {
        this.addToBatch('error', message, meta);
    }

    /**
     * Get new correlation ID for request tracing
     */
    getNewCorrelationId() {
        this.correlationId = this.generateCorrelationId();
        return this.correlationId;
    }

    /**
     * Set correlation ID for request tracing
     */
    setCorrelationId(correlationId) {
        this.correlationId = correlationId;
    }

    /**
     * Get current logger stats
     */
    getStats() {
        return {
            sessionId: this.sessionId,
            correlationId: this.correlationId,
            batchQueueSize: this.batchQueue.length,
            timingsActive: this.timings.size,
            isElectron: this.isElectron,
            batchSize: this.batchSize,
            batchTimeoutMs: this.batchTimeoutMs
        };
    }

    /**
     * Persist the current batch queue to localStorage
     */
    persistBatchQueue() {
        if (typeof localStorage === 'undefined') return;
        
        try {
            if (this.batchQueue.length > 0) {
                const existingLogs = this.getPersistedLogs();
                const newLogs = [...existingLogs, ...this.batchQueue];
                
                // Ensure we don't exceed maximum logs
                const logsToKeep = newLogs.slice(-this.maxLogs);
                
                // Check storage size and trim if needed
                const logsJson = JSON.stringify(logsToKeep);
                if (logsJson.length > this.maxStorageSize) {
                    // Remove oldest logs until we're under the size limit
                    let trimmedLogs = [...logsToKeep];
                    while (trimmedLogs.length > 1 && JSON.stringify(trimmedLogs).length > this.maxStorageSize) {
                        trimmedLogs.shift();
                    }
                    localStorage.setItem(this.storageKey, JSON.stringify(trimmedLogs));
                    if (!this.storageWarningShown) {
                        console.warn('Log storage limit reached. Older logs have been removed.');
                        this.storageWarningShown = true;
                    }
                } else {
                    localStorage.setItem(this.storageKey, logsJson);
                }
            }
        } catch (error) {
            console.error('Failed to persist logs:', error);
            // If we can't write to localStorage, clear it to prevent further errors
            try { localStorage.removeItem(this.storageKey); } catch (e) {}
        }
    }

    /**
     * Get persisted logs from localStorage
     */
    getPersistedLogs() {
        if (typeof localStorage === 'undefined') return [];
        
        try {
            const logs = localStorage.getItem(this.storageKey);
            return logs ? JSON.parse(logs) : [];
        } catch (error) {
            console.error('Failed to load persisted logs:', error);
            return [];
        }
    }

    /**
     * Flush any persisted logs from previous sessions
     */
    async flushPersistedLogs() {
        if (this.isElectron || typeof localStorage === 'undefined') return;
        
        try {
            const persistedLogs = this.getPersistedLogs();
            if (persistedLogs.length === 0) return;

            // Clear the persisted logs first to prevent duplicates if there's an error
            localStorage.removeItem(this.storageKey);
            
            // Send logs in batches to avoid overwhelming the system
            const batchSize = 10;
            for (let i = 0; i < persistedLogs.length; i += batchSize) {
                const batch = persistedLogs.slice(i, i + batchSize);
                await this.sendBatchToBackend(batch);
            }
        } catch (error) {
            console.error('Failed to flush persisted logs:', error);
        }
    }

    /**
     * Send a batch of logs to the backend
     */
    async sendBatchToBackend(batch) {
        if (this.isElectron) {
            try {
                // Use Promise.all to send logs in parallel but wait for all to complete
                await Promise.all(batch.map(({ level, entry }) => 
                    window.lifeboard.log[level.toLowerCase()](entry)
                ));
            } catch (error) {
                console.error('Failed to send batch to backend:', error);
                throw error; // Re-throw to trigger retry in flushPersistedLogs
            }
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }
        // Persist any remaining logs before cleanup
        this.persistBatchQueue();
        this.flushBatch();
        this.timings.clear();

        this.info('Frontend logger cleanup completed');
    }
}

// Create singleton instance
const logger = new FrontendLogger();

// Helper function to create a scoped logger
export function createScopedLogger(component, context = {}) {
    return {
        debug: (message, meta = {}) => 
            logger.debug(message, { ...meta, component, ...context }),
        info: (message, meta = {}) => 
            logger.info(message, { ...meta, component, ...context }),
        warn: (message, meta = {}) => 
            logger.warn(message, { ...meta, component, ...context }),
        error: (message, error, meta = {}) => 
            logger.error(message, error, { ...meta, component, ...context }),
        withDebugContext: (name, fn) => 
            logger.withDebugContext(`${component}:${name}`, fn, context),
        withPerformanceTracking: (fn, name) => 
            logger.withPerformanceTracking(fn, name || `${component}:${fn.name}`)
    };
}

// Export for use in other modules
export default logger;

// Auto-cleanup on page unload
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        logger.cleanup();
    });
}
