/**
 * Reusable API key input component with validation UI
 *
 * Provides a standardized interface for collecting API keys with
 * built-in validation feedback and security features.
 */
import React, { useState } from 'react';
import { SecureInput, Button, ValidationStatus } from '../ui/components';

const APIKeyInput = ({ 
  onValidate, 
  onSave, 
  pluginId, 
  apiKeyLabel = "API Key",
  apiKeyHint = "Enter your API key" 
}) => {
  const [apiKey, setApiKey] = useState('');
  const [showKey, setShowKey] = useState(false);
  const [validating, setValidating] = useState(false);
  const [validationStatus, setValidationStatus] = useState(null);

  const handleValidate = async () => {
    if (!apiKey.trim()) {
      setValidationStatus({ success: false, message: 'API key is required' });
      return;
    }

    setValidating(true);
    setValidationStatus({ pending: true, message: 'Validating API key...' });
    
    try {
      const result = await onValidate(apiKey.trim());
      setValidationStatus(result);
    } catch (error) {
      setValidationStatus({ 
        success: false, 
        message: error.message || 'Validation failed' 
      });
    } finally {
      setValidating(false);
    }
  };

  return (
    <div className="api-key-input">
      <label htmlFor="apiKey">
        <span className="label-text">{apiKeyLabel}</span>
        <span className="label-hint">{apiKeyHint}</span>
      </label>
      <div className="input-with-button">
        <SecureInput
          id="apiKey"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          type={showKey ? 'text' : 'password'}
          placeholder={apiKeyHint}
          autoComplete="off"
        />
        <Button 
          onClick={handleValidate} 
          disabled={validating || !apiKey.trim()}
          className="btn-secondary"
        >
          {validating ? 'Validating...' : 'Validate'}
        </Button>
        <Button 
          onClick={() => setShowKey(!showKey)} 
          className="btn-icon"
          title={showKey ? 'Hide API Key' : 'Show API Key'}
        >
          {showKey ? '👁️‍🗨️' : '👁️'}
        </Button>
      </div>
      {validationStatus && (
        <ValidationStatus status={validationStatus} />
      )}
    </div>
  );
};

export default APIKeyInput;