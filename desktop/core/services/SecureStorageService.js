/**
 * Secure Storage Service
 *
 * Provides a standardized interface for securely storing and retrieving
 * sensitive information like API keys in the Supabase PostgreSQL database.
 */
const { encryptString, decryptString } = require('../pluginAPI/cryptoUtilities');
const { getClient } = require('../supabase/client');
const { factory: createLogger } = require('../logger/CoreLogger');

const log = createLogger('secure-storage-service');

class SecureStorageService {
  constructor() {
    this.initialized = false;
    this.encryptionKey = null;
  }

  /**
   * Initialize the secure storage service
   *
   * Args
   * ----
   * options: Initialization options
   *
   * Returns
   * -------
   * Promise resolving when initialization is complete
   *
   * Example
   * -------
   * >>> await secureStorageService.initialize();
   */
  async initialize(options = {}) {
    if (this.initialized) return;
    
    try {
      log.INFO('Initializing secure storage service');
      
      // Load encryption key from system keychain or environment
      this.encryptionKey = await this.loadEncryptionKey();
      
      // Create database tables if they don't exist
      await this.ensureTablesExist();
      
      this.initialized = true;
      log.INFO('Secure storage service initialized');
    } catch (error) {
      log.ERROR('Failed to initialize secure storage service', { error: error.message });
      throw error;
    }
  }

  /**
   * Store API key securely
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   * apiKey: API key to store
   * metadata: Optional metadata
   *
   * Returns
   * -------
   * Promise resolving to success status
   *
   * Example
   * -------
   * >>> await secureStorageService.storeAPIKey('github', 'default', 'abc123');
   */
  async storeAPIKey(pluginId, keyName, apiKey, metadata = {}) {
    if (!this.initialized) await this.initialize();
    
    try {
      log.INFO('Storing API key', { pluginId, keyName });
      
      // Encrypt the API key
      const encryptedKey = encryptString(apiKey);
      
      // Store in database
      const supabase = getClient();
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .upsert({
          plugin_id: pluginId,
          key_name: keyName,
          encrypted_key: encryptedKey,
          metadata: metadata,
          updated_at: new Date().toISOString()
        }, { onConflict: ['plugin_id', 'key_name'] });
      
      if (error) throw error;
      
      log.INFO('API key stored successfully', { pluginId, keyName });
      return true;
    } catch (error) {
      log.ERROR('Failed to store API key', { pluginId, keyName, error: error.message });
      throw error;
    }
  }

  /**
   * Retrieve API key
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to the API key or null if not found
   *
   * Example
   * -------
   * >>> const apiKey = await secureStorageService.retrieveAPIKey('github', 'default');
   */
  async retrieveAPIKey(pluginId, keyName = 'default') {
    if (!this.initialized) await this.initialize();
    
    try {
      log.INFO('Retrieving API key', { pluginId, keyName });
      
      // Retrieve from database
      const supabase = getClient();
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .select('encrypted_key')
        .eq('plugin_id', pluginId)
        .eq('key_name', keyName)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          log.INFO('No API key found', { pluginId, keyName });
          return null;
        }
        throw error;
      }
      
      if (!data || !data.encrypted_key) {
        log.INFO('No API key found', { pluginId, keyName });
        return null;
      }
      
      // Decrypt the API key
      const apiKey = await decryptString(data.encrypted_key, this.encryptionKey);
      
      log.INFO('API key retrieved successfully', { pluginId, keyName });
      return apiKey;
    } catch (error) {
      log.ERROR('Failed to retrieve API key', { pluginId, keyName, error: error.message });
      throw error;
    }
  }

  /**
   * Set API key validation status
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   * isValid: Validation status
   *
   * Returns
   * -------
   * Promise resolving to success status
   *
   * Example
   * -------
   * >>> await secureStorageService.setAPIKeyValidationStatus('github', 'default', true);
   */
  async setAPIKeyValidationStatus(pluginId, keyName, isValid) {
    if (!this.initialized) await this.initialize();
    
    try {
      log.INFO('Setting API key validation status', { pluginId, keyName, isValid });
      
      // Update in database
      const supabase = getClient();
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .update({
          is_valid: isValid,
          last_validated_at: new Date().toISOString()
        })
        .eq('plugin_id', pluginId)
        .eq('key_name', keyName);
      
      if (error) throw error;
      
      log.INFO('API key validation status updated', { pluginId, keyName, isValid });
      return true;
    } catch (error) {
      log.ERROR('Failed to update API key validation status', {
        pluginId, keyName, isValid, error: error.message
      });
      throw error;
    }
  }

  /**
   * Get API key validation status
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to validation status object
   *
   * Example
   * -------
   * >>> const status = await secureStorageService.getAPIKeyValidationStatus('github', 'default');
   * >>> console.log(status.isValid); // true or false
   */
  async getAPIKeyValidationStatus(pluginId, keyName = 'default') {
    if (!this.initialized) await this.initialize();
    
    try {
      log.INFO('Getting API key validation status', { pluginId, keyName });
      
      // Retrieve from database
      const supabase = getClient();
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .select('is_valid, last_validated_at')
        .eq('plugin_id', pluginId)
        .eq('key_name', keyName)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          log.INFO('No API key found for validation status', { pluginId, keyName });
          return { isValid: false, lastValidatedAt: null };
        }
        throw error;
      }
      
      if (!data) {
        log.INFO('No API key found for validation status', { pluginId, keyName });
        return { isValid: false, lastValidatedAt: null };
      }
      
      log.INFO('API key validation status retrieved', {
        pluginId, keyName, isValid: data.is_valid
      });
      
      return {
        isValid: data.is_valid || false,
        lastValidatedAt: data.last_validated_at
      };
    } catch (error) {
      log.ERROR('Failed to get API key validation status', {
        pluginId, keyName, error: error.message
      });
      throw error;
    }
  }

  /**
   * Delete API key
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to success status
   *
   * Example
   * -------
   * >>> await secureStorageService.deleteAPIKey('github', 'default');
   */
  async deleteAPIKey(pluginId, keyName = 'default') {
    if (!this.initialized) await this.initialize();
    
    try {
      log.INFO('Deleting API key', { pluginId, keyName });
      
      // Delete from database
      const supabase = getClient();
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .delete()
        .eq('plugin_id', pluginId)
        .eq('key_name', keyName);
      
      if (error) throw error;
      
      log.INFO('API key deleted successfully', { pluginId, keyName });
      return true;
    } catch (error) {
      log.ERROR('Failed to delete API key', { pluginId, keyName, error: error.message });
      throw error;
    }
  }

  /**
   * Load encryption key from secure storage
   *
   * Returns
   * -------
   * Promise resolving to encryption key
   *
   * Example
   * -------
   * >>> const key = await secureStorageService.loadEncryptionKey();
   */
  async loadEncryptionKey() {
    // Implementation depends on platform-specific secure storage
    // This could use system keychain, environment variables, etc.
    // For now, return a placeholder
    return process.env.ENCRYPTION_KEY || 'default-encryption-key';
  }

  /**
   * Ensure database tables exist
   *
   * Returns
   * -------
   * Promise resolving when tables are created
   *
   * Example
   * -------
   * >>> await secureStorageService.ensureTablesExist();
   */
  async ensureTablesExist() {
    try {
      const supabase = getClient();

      // Check if table exists
      const { data, error } = await supabase
        .from('plugin_api_keys')
        .select('count(*)')
        .limit(1);

      if (error && error.code !== 'PGRST116') {
        // Table doesn't exist, create it
        await supabase.rpc('create_plugin_api_keys_table');
      }
    } catch (error) {
      log.ERROR('Failed to ensure tables exist', { error: error.message });
      throw error;
    }
  }
}

// Singleton instance
const secureStorageService = new SecureStorageService();

module.exports = {
  secureStorageService
};