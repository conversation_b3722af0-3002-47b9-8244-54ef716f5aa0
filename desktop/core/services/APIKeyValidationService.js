/**
 * API Key Validation Service
 *
 * Provides a standardized interface for validating API keys
 * with comprehensive retry logic and error handling.
 */
const { factory: createLogger } = require('../logger/CoreLogger');
const { secretManager } = require('../secretManager/SecretManager');
const EventBus = require('../../src/EventBus');

const log = createLogger('api-key-validation-service');

class APIKeyValidationService {
  constructor() {
    this.validationStates = new Map(); // pluginId -> validation state
    this.retryStates = new Map(); // pluginId -> retry state
    this.validationHandlers = new Map(); // pluginId -> validation handler
    
    // Listen for validation completion events
    EventBus.on('apikey:validation:complete', this.handleValidationComplete.bind(this));
  }

  /**
   * Register a validation handler for a specific plugin
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * handler: Function that validates an API key and returns {success, error}
   *
   * Example
   * -------
   * >>> apiKeyValidationService.registerHandler('github', async (apiKey) => {
   * >>>   const response = await fetch('https://api.github.com/user', {
   * >>>     headers: { 'Authorization': `token ${apiKey}` }
   * >>>   });
   * >>>   return { success: response.ok, error: response.ok ? null : 'Invalid API key' };
   * >>> });
   */
  registerHandler(pluginId, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Validation handler must be a function');
    }
    this.validationHandlers.set(pluginId, handler);
    log.INFO('Registered validation handler', { pluginId });
  }

  /**
   * Validate API key with retry logic
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * apiKey: API key to validate
   * options: Validation options
   *
   * Returns
   * -------
   * Promise resolving to validation result {success, error}
   *
   * Example
   * -------
   * >>> const result = await apiKeyValidationService.validateAPIKey('github', 'abc123');
   * >>> console.log(result.success); // true or false
   */
  async validateAPIKey(pluginId, apiKey, options = {}) {
    const correlationId = options.correlationId || Date.now().toString();
    const logContext = { pluginId, correlationId, keyLength: apiKey?.length || 0 };
    
    try {
      log.INFO('Starting API key validation', logContext);
      
      // Input validation
      if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
        const error = 'API key is required and must be a non-empty string';
        log.WARN('Validation failed: Invalid API key format', { ...logContext, error });
        return { success: false, error };
      }
      
      // Check for registered handler
      const handler = this.validationHandlers.get(pluginId);
      if (!handler) {
        const error = `No validation handler registered for plugin: ${pluginId}`;
        log.ERROR(error, logContext);
        return { success: false, error };
      }
      
      // Track validation state
      this.validationStates.set(pluginId, { inProgress: true, timestamp: Date.now() });
      
      // Perform validation with retry logic
      let validationResult = false;
      let lastError = null;
      
      // Retry loop with exponential backoff
      for (let attempt = 1; attempt <= 3; attempt++) {
        const attemptContext = { ...logContext, attempt };
        
        try {
          log.INFO('Validation attempt', attemptContext);
          
          // Call the registered validation handler
          const result = await handler(apiKey);
          
          if (result.success) {
            validationResult = true;
            log.INFO('API key validation successful', attemptContext);
            break;
          } else {
            lastError = new Error(result.error || 'API key validation failed');
            log.WARN('API key validation failed', { ...attemptContext, error: result.error });
          }
        } catch (error) {
          lastError = error;
          log.WARN('Validation attempt failed', {
            ...attemptContext,
            error: error.message
          });
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < 3 && !validationResult) {
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s
          log.DEBUG('Waiting before retry', { ...attemptContext, delayMs: delay });
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
      
      // Update validation status in database
      await this.updateValidationStatus(pluginId, 'default', validationResult);
      
      // Update validation state
      this.validationStates.set(pluginId, { 
        inProgress: false, 
        isValid: validationResult, 
        timestamp: Date.now(),
        lastError: lastError?.message
      });
      
      // Emit completion event
      this.emitValidationComplete(pluginId, validationResult, correlationId, lastError);
      
      return {
        success: validationResult,
        error: lastError?.message || null
      };
    } catch (error) {
      log.ERROR('Unexpected error during validation', { ...logContext, error: error.message });
      
      // Update validation state
      this.validationStates.set(pluginId, { 
        inProgress: false, 
        isValid: false, 
        timestamp: Date.now(),
        lastError: error.message
      });
      
      return {
        success: false,
        error: error.message || 'An unexpected error occurred during validation'
      };
    }
  }

  /**
   * Update validation status in database
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * keyName: Key name (usually 'default')
   * isValid: Validation result
   *
   * Example
   * -------
   * >>> await apiKeyValidationService.updateValidationStatus('github', 'default', true);
   */
  async updateValidationStatus(pluginId, keyName, isValid) {
    try {
      await secretManager.setAPIKeyValidationStatus(pluginId, keyName, isValid);
      log.DEBUG('Validation status updated', { pluginId, keyName, isValid });
    } catch (error) {
      log.ERROR('Failed to update validation status', {
        pluginId, keyName, isValid, error: error.message
      });
    }
  }

  /**
   * Emit validation completion event
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   * isValid: Validation result
   * correlationId: Correlation ID for tracking
   * error: Error object if validation failed
   */
  emitValidationComplete(pluginId, isValid, correlationId, error) {
    const eventData = {
      pluginId,
      isValid,
      correlationId,
      timestamp: new Date().toISOString(),
      error: error ? { message: error.message, code: error.code } : null
    };

    EventBus.emit('apikey:validation:complete', eventData);
    log.DEBUG('Validation completion event emitted', eventData);
  }

  /**
   * Handle validation completion event
   *
   * Args
   * ----
   * eventData: Event data from validation completion
   */
  handleValidationComplete(eventData) {
    const { pluginId, isValid } = eventData;
    log.DEBUG('Received validation completion event', { pluginId, isValid });
    
    // Update validation state
    const currentState = this.validationStates.get(pluginId) || {};
    this.validationStates.set(pluginId, {
      ...currentState,
      inProgress: false,
      isValid,
      timestamp: Date.now()
    });
  }
}

// Singleton instance
const apiKeyValidationService = new APIKeyValidationService();

module.exports = {
  apiKeyValidationService
};