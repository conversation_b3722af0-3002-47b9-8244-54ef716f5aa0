/**
 * CoreLogger - Universal logging module for Lifeboard Desktop Application
 *
 * Features:
 * - JSON structured logging per schema
 * - Daily file rotation with retention
 * - Runtime log level configuration
 * - Safe concurrent writes using pino
 * - Unhandled exception capture
 * - Fallback to stderr on file system errors
 * - Schema validation and sanitization
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

const fs = require('fs');
const path = require('path');
const pino = require('pino');
const { Worker } = require('worker_threads');

/**
 * CoreLogger provides unified, production-grade logging across all Lifeboard components
 *
 * Log Schema:
 * - ts: ISO-8601 timestamp (UTC)
 * - level: DEBUG|INFO|WARN|ERROR|FATAL
 * - component: desktop|plugin:<name>|backend:<service>
 * - msg: Human-readable message
 * - meta: Free-form metadata object
 * - corrId: Cross-service correlation ID (optional)
 */
class CoreLogger {
    /**
     * Initialize CoreLogger with configuration
     * @param {Object} options - Configuration options
     * @param {string} options.component - Default component name
     * @param {string} options.logLevel - Initial log level (default: DEBUG)
     * @param {string} options.logDir - Base log directory (default: project_dir/logs)
     * @param {boolean} options.setupProcessHandlers - Whether to setup process event handlers (default: true)
     */
    constructor(options = {}) {
        this.component = options.component || 'desktop';
        // Default to TRACE level for maximum verbosity
        this.logLevel = process.env.LIFEBOARD_LOG_LEVEL || options.logLevel || 'TRACE';
        this.logDir = options.logDir || this._getDefaultLogDir();
        this.setupProcessHandlers = options.setupProcessHandlers !== false;
        this.fallbackToStderr = false;
        this.logger = null;
        this.destinations = new Map();

        // Log level mapping with TRACE as most verbose
        this.levelMap = {
            'TRACE': 10,
            'DEBUG': 20,
            'INFO': 30,
            'WARN': 40,
            'ERROR': 50,
            'FATAL': 60
        };

        this._validateLogLevel();
        this._ensureLogDirectory();
        this._setupPinoLogger();
        if (this.setupProcessHandlers) {
            this._setupExceptionHandlers();
        }
        this._setupCleanupScheduler();

        this.TRACE('CoreLogger initialized', {
            component: this.component,
            logLevel: this.logLevel,
            logDir: this.logDir
        });
    }

    /**
     * Get default log directory based on project structure
     * @returns {string} Default log directory path
     * @private
     */
    _getDefaultLogDir() {
        // Find project root by looking for package.json or docker-compose.yml
        let currentDir = process.cwd();
        while (currentDir !== path.dirname(currentDir)) {
            if (fs.existsSync(path.join(currentDir, 'package.json')) ||
                fs.existsSync(path.join(currentDir, 'docker-compose.yml'))) {
                return path.join(currentDir, 'logs');
            }
            currentDir = path.dirname(currentDir);
        }
        // Fallback to current working directory
        return path.join(process.cwd(), 'logs');
    }

    /**
     * Validate log level configuration
     * @private
     */
    _validateLogLevel() {
        if (!this.levelMap[this.logLevel]) {
            console.warn(`Invalid log level: ${this.logLevel}. Defaulting to TRACE`);
            this.logLevel = 'TRACE';
        }
    }

    /**
     * Ensure log directory exists with proper permissions
     * @private
     */
    _ensureLogDirectory() {
        try {
            if (!fs.existsSync(this.logDir)) {
                fs.mkdirSync(this.logDir, { recursive: true, mode: 0o700 });
            }

            // Test write permissions
            const testFile = path.join(this.logDir, '.write-test');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);

        } catch (error) {
            console.warn(`Cannot write to log directory ${this.logDir}:`, error.message);
            console.warn('Falling back to stderr logging');
            this.fallbackToStderr = true;
        }
    }

    /**
     * Setup Pino logger with proper configuration
     * @private
     */
    _setupPinoLogger() {
        const pinoConfig = {
            level: this.logLevel.toLowerCase(),
            timestamp: () => `,"ts":"${new Date().toISOString()}"`,
            formatters: {
                level: (label) => ({ level: label.toUpperCase() }),
                bindings: () => ({})
            },
            redact: {
                paths: ['password', 'token', 'apiKey', 'secret'],
                censor: '[REDACTED]'
            }
        };

        if (this.fallbackToStderr) {
            this.logger = pino(pinoConfig);
        } else {
            // Setup daily rotation destination
            const destination = this._createDailyDestination();
            this.logger = pino(pinoConfig, destination);
        }
    }

    /**
     * Create daily rotation destination for pino
     * @returns {Object} Pino destination object
     * @private
     */
    _createDailyDestination() {
        const today = new Date().toISOString().split('T')[0];
        const logFile = path.join(this.logDir, `${today}-${this.component}.log`);

        try {
            const destination = pino.destination({
                dest: logFile,
                sync: false,
                mkdir: true
            });

            this.destinations.set(today, destination);
            return destination;

        } catch (error) {
            console.warn(`Failed to create log destination: ${error.message}`);
            this.fallbackToStderr = true;
            return pino.destination(1); // stdout
        }
    }

    /**
     * Setup unhandled exception and rejection handlers
     * @private
     */
    _setupExceptionHandlers() {
        // Unhandled exceptions
        process.on('uncaughtException', (error) => {
            this.FATAL('Uncaught Exception', {
                error: error.message,
                stack: error.stack,
                meta: { uncaught: true }
            });

            // Give time for log to flush before exit
            setTimeout(() => process.exit(1), 100);
        });

        // Unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            this.ERROR('Unhandled Promise Rejection', {
                reason: reason?.toString() || 'Unknown reason',
                meta: { unhandledRejection: true }
            });
        });

        // Electron renderer process error handling (if in renderer)
        if (typeof window !== 'undefined') {
            window.addEventListener('error', (event) => {
                this.ERROR('Window Error', {
                    message: event.error?.message || event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    meta: { windowError: true }
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.ERROR('Window Unhandled Rejection', {
                    reason: event.reason?.toString() || 'Unknown reason',
                    meta: { windowUnhandledRejection: true }
                });
            });
        }
    }

    /**
     * Setup cleanup scheduler for log retention
     * @private
     */
    _setupCleanupScheduler() {
        // Run cleanup every 24 hours
        setInterval(() => {
            this._cleanupOldLogs();
        }, 24 * 60 * 60 * 1000);

        // Initial cleanup
        setTimeout(() => {
            this._cleanupOldLogs();
        }, 5000);
    }

    /**
     * Clean up old log files based on retention policy
     * @private
     */
    _cleanupOldLogs() {
        try {
            if (!fs.existsSync(this.logDir)) return;

            const files = fs.readdirSync(this.logDir);
            const now = new Date();
            const cutoffDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000)); // 30 days
            const compressDate = new Date(now.getTime() - (14 * 24 * 60 * 60 * 1000)); // 14 days

            files.forEach(file => {
                const filePath = path.join(this.logDir, file);
                const stats = fs.statSync(filePath);

                if (stats.mtime < cutoffDate) {
                    // Delete files older than 30 days
                    fs.unlinkSync(filePath);
                    this.DEBUG('Deleted old log file', { file });
                } else if (stats.mtime < compressDate && !file.endsWith('.gz')) {
                    // TODO: Compress files older than 14 days
                    this.DEBUG('File eligible for compression', { file });
                }
            });

        } catch (error) {
            this.WARN('Log cleanup failed', { error: error.message });
        }
    }

    /**
     * Set runtime log level
     * @param {string} level - New log level (DEBUG|INFO|WARN|ERROR|FATAL)
     */
    setLevel(level) {
        const upperLevel = level.toUpperCase();
        if (!this.levelMap[upperLevel]) {
            this.WARN('Invalid log level provided', {
                requestedLevel: level,
                validLevels: Object.keys(this.levelMap)
            });
            return;
        }

        const oldLevel = this.logLevel;
        this.logLevel = upperLevel;

        if (this.logger) {
            this.logger.level = level.toLowerCase();
        }

        this.INFO('Log level changed', {
            oldLevel,
            newLevel: this.logLevel
        });
    }

    /**
     * Get current log level
     * @returns {string} Current log level
     */
    getLevel() {
        return this.logLevel;
    }

    /**
     * Sanitize metadata to remove sensitive information
     * @param {Object} meta - Metadata object
     * @returns {Object} Sanitized metadata
     * @private
     */
    _sanitizeMeta(meta) {
        if (!meta || typeof meta !== 'object') {
            return meta;
        }

        const sanitized = JSON.parse(JSON.stringify(meta));
        const sensitivePatterns = [
            /^password$/i,
            /^token$/i,
            /^apikey$/i,
            /^secret$/i,
            /^auth$/i,
            /token$/i,     // matches authToken, accessToken, etc.
            /apikey$/i,    // matches apiKey, etc.
            /secretkey$/i, // matches secretKey, etc.
            /password$/i   // matches userPassword, etc.
        ];

        const sanitizeRecursive = (obj) => {
            Object.keys(obj).forEach(key => {
                if (sensitivePatterns.some(pattern => pattern.test(key))) {
                    obj[key] = '[REDACTED]';
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    sanitizeRecursive(obj[key]);
                }
            });
        };

        sanitizeRecursive(sanitized);
        return sanitized;
    }

    /**
     * Core logging method that validates schema and writes log entry
     * @param {string} level - Log level
     * @param {string} msg - Log message
     * @param {Object} meta - Metadata object
     * @param {string} corrId - Correlation ID (optional)
     * @private
     */
    _log(level, msg, meta = {}, corrId = null) {
        try {
            // Validate and prepare log entry
            const logEntry = {
                component: this.component,
                msg: String(msg),
                meta: this._sanitizeMeta(meta)
            };

            if (corrId) {
                logEntry.corrId = corrId;
            }

            // Use pino to log (it will add ts and level automatically)
            this.logger[level.toLowerCase()](logEntry);

        } catch (error) {
            // Fallback logging to stderr if main logging fails
            console.error(`Logging failed: ${error.message}`);
            console.error(`Original log: [${level}] ${msg}`, meta);
        }
    }

    /**
     * Log TRACE level message (most verbose)
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    TRACE(msg, meta = {}, corrId = null) {
        this._log('TRACE', msg, meta, corrId);
    }

    /**
     * Log DEBUG level message
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    DEBUG(msg, meta = {}, corrId = null) {
        this._log('DEBUG', msg, meta, corrId);
    }

    /**
     * Log INFO level message
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    INFO(msg, meta = {}, corrId = null) {
        this._log('INFO', msg, meta, corrId);
    }

    /**
     * Log WARN level message
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    WARN(msg, meta = {}, corrId = null) {
        this._log('WARN', msg, meta, corrId);
    }

    /**
     * Log ERROR level message
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    ERROR(msg, meta = {}, corrId = null) {
        this._log('ERROR', msg, meta, corrId);
    }

    /**
     * Log FATAL level message
     * @param {string} msg - Log message
     * @param {Object} meta - Optional metadata
     * @param {string} corrId - Optional correlation ID
     */
    FATAL(msg, meta = {}, corrId = null) {
        this._log('FATAL', msg, meta, corrId);
    }

    /**
     * Create a child logger with bound component
     * @param {string} component - Component name for child logger
     * @returns {CoreLogger} Child logger instance
     */
    child(component) {
        const childOptions = {
            component,
            logLevel: this.logLevel,
            logDir: this.logDir
        };

        const childLogger = new CoreLogger(childOptions);

        this.DEBUG('Created child logger', {
            parentComponent: this.component,
            childComponent: component
        });

        return childLogger;
    }

    /**
     * Get logger statistics
     * @returns {Object} Logger statistics
     */
    getStats() {
        return {
            component: this.component,
            logLevel: this.logLevel,
            logDir: this.logDir,
            fallbackMode: this.fallbackToStderr,
            destinationCount: this.destinations.size
        };
    }

    /**
     * Flush all pending log writes (useful for testing and shutdown)
     * @returns {Promise} Promise that resolves when all writes are flushed
     */
    async flush() {
        return new Promise((resolve) => {
            if (this.logger && this.logger.flush) {
                this.logger.flush(() => resolve());
            } else {
                resolve();
            }
        });
    }

    /**
     * Graceful shutdown - flush logs and cleanup
     * @returns {Promise} Promise that resolves when shutdown is complete
     */
    async shutdown() {
        this.INFO('CoreLogger shutting down', { component: this.component });

        await this.flush();

        // Close all destinations
        this.destinations.forEach((destination, date) => {
            if (destination && destination.end) {
                destination.end();
            }
        });

        this.destinations.clear();
    }
}

// Singleton instance for global access
let globalLogger = null;

/**
 * Factory function to create component-specific loggers
 * @param {string} component - Component name (e.g., 'plugin:limitless', 'backend:postgres')
 * @returns {CoreLogger} Component-specific logger instance
 */
function factory(component) {
    if (!globalLogger) {
        globalLogger = new CoreLogger({ component: 'desktop' });
    }

    return globalLogger.child(component);
}

/**
 * Get or create the global logger instance
 * @returns {CoreLogger} Global logger instance
 */
function getGlobalLogger() {
    if (!globalLogger) {
        globalLogger = new CoreLogger({ component: 'desktop' });
    }
    return globalLogger;
}

// Export singleton instance and factory
module.exports = {
    CoreLogger,
    logger: getGlobalLogger(),
    factory,
    getGlobalLogger
};
