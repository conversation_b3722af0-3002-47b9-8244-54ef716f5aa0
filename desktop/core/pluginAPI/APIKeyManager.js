/**
 * Plugin API Key Manager
 *
 * Provides a standardized interface for plugins to manage API keys
 * with validation, storage, and retrieval functionality.
 */
const { apiKeyValidationService } = require('../services/APIKeyValidationService');
const { secureStorageService } = require('../services/SecureStorageService');
const { factory: createLogger } = require('../logger/CoreLogger');
const EventBus = require('../../src/EventBus');

const log = createLogger('plugin-api-key-manager');

class APIKeyManager {
  /**
   * Create a new APIKeyManager instance for a specific plugin
   *
   * Args
   * ----
   * pluginId: Plugin identifier
   *
   * Example
   * -------
   * >>> const apiKeyManager = new APIKeyManager('github');
   */
  constructor(pluginId) {
    this.pluginId = pluginId;
    this.logger = createLogger(`plugin-api-key-manager:${pluginId}`);
  }

  /**
   * Register a validation handler for this plugin
   *
   * Args
   * ----
   * handler: Function that validates an API key and returns {success, error}
   *
   * Example
   * -------
   * >>> apiKeyManager.registerValidationHandler(async (apiKey) => {
   * >>>   const response = await fetch('https://api.github.com/user', {
   * >>>     headers: { 'Authorization': `token ${apiKey}` }
   * >>>   });
   * >>>   return { success: response.ok, error: response.ok ? null : 'Invalid API key' };
   * >>> });
   */
  registerValidationHandler(handler) {
    apiKeyValidationService.registerHandler(this.pluginId, handler);
    this.logger.INFO('Registered validation handler');
  }

  /**
   * Validate and store API key
   *
   * Args
   * ----
   * apiKey: API key to validate and store
   * options: Validation and storage options
   *
   * Returns
   * -------
   * Promise resolving to validation result {success, error}
   *
   * Example
   * -------
   * >>> const result = await apiKeyManager.validateAndStoreAPIKey('abc123');
   * >>> console.log(result.success); // true or false
   */
  async validateAndStoreAPIKey(apiKey, options = {}) {
    const keyName = options.keyName || 'default';
    const correlationId = options.correlationId || Date.now().toString();
    const metadata = options.metadata || {};
    
    try {
      this.logger.INFO('Validating and storing API key', {
        keyName, correlationId, keyLength: apiKey?.length || 0
      });
      
      // Validate the API key
      const validationResult = await apiKeyValidationService.validateAPIKey(
        this.pluginId, apiKey, { correlationId }
      );
      
      // Only store if validation was successful
      if (validationResult.success) {
        this.logger.INFO('API key validated successfully, storing', { keyName });
        
        // Store the API key
        await secureStorageService.storeAPIKey(
          this.pluginId, keyName, apiKey, {
            ...metadata,
            isValid: true,
            lastValidatedAt: new Date().toISOString()
          }
        );
        
        // Emit event for successful storage
        EventBus.emit('apikey:stored', {
          pluginId: this.pluginId,
          keyName,
          isValid: true,
          correlationId
        });
      } else {
        this.logger.WARN('API key validation failed, not storing', {
          keyName, error: validationResult.error
        });
        
        // Emit event for failed validation
        EventBus.emit('apikey:validation:failed', {
          pluginId: this.pluginId,
          keyName,
          error: validationResult.error,
          correlationId
        });
      }
      
      return validationResult;
    } catch (error) {
      this.logger.ERROR('Failed to validate and store API key', {
        keyName, error: error.message
      });
      
      // Emit event for error
      EventBus.emit('apikey:error', {
        pluginId: this.pluginId,
        keyName,
        error: error.message,
        correlationId
      });
      
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      };
    }
  }

  /**
   * Validate API key without storing
   *
   * Args
   * ----
   * apiKey: API key to validate
   * options: Validation options
   *
   * Returns
   * -------
   * Promise resolving to validation result {success, error}
   *
   * Example
   * -------
   * >>> const result = await apiKeyManager.validateAPIKey('abc123');
   * >>> console.log(result.success); // true or false
   */
  async validateAPIKey(apiKey, options = {}) {
    const correlationId = options.correlationId || Date.now().toString();
    
    try {
      this.logger.INFO('Validating API key', {
        correlationId, keyLength: apiKey?.length || 0
      });
      
      // Validate the API key
      return await apiKeyValidationService.validateAPIKey(
        this.pluginId, apiKey, { correlationId }
      );
    } catch (error) {
      this.logger.ERROR('Failed to validate API key', {
        error: error.message
      });
      
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      };
    }
  }

  /**
   * Retrieve stored API key
   *
   * Args
   * ----
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to API key or null if not found
   *
   * Example
   * -------
   * >>> const apiKey = await apiKeyManager.retrieveAPIKey();
   */
  async retrieveAPIKey(keyName = 'default') {
    try {
      this.logger.INFO('Retrieving API key', { keyName });
      
      // Retrieve the API key
      return await secureStorageService.retrieveAPIKey(this.pluginId, keyName);
    } catch (error) {
      this.logger.ERROR('Failed to retrieve API key', {
        keyName, error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get API key validation status
   *
   * Args
   * ----
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to validation status object
   *
   * Example
   * -------
   * >>> const status = await apiKeyManager.getValidationStatus();
   * >>> console.log(status.isValid); // true or false
   */
  async getValidationStatus(keyName = 'default') {
    try {
      this.logger.INFO('Getting API key validation status', { keyName });
      
      // Get validation status
      return await secureStorageService.getAPIKeyValidationStatus(this.pluginId, keyName);
    } catch (error) {
      this.logger.ERROR('Failed to get API key validation status', {
        keyName, error: error.message
      });
      
      return { isValid: false, lastValidatedAt: null };
    }
  }

  /**
   * Delete API key
   *
   * Args
   * ----
   * keyName: Key name (usually 'default')
   *
   * Returns
   * -------
   * Promise resolving to success status
   *
   * Example
   * -------
   * >>> await apiKeyManager.deleteAPIKey();
   */
  async deleteAPIKey(keyName = 'default') {
    try {
      this.logger.INFO('Deleting API key', { keyName });
      
      // Delete the API key
      await secureStorageService.deleteAPIKey(this.pluginId, keyName);
      
      // Emit event for deletion
      EventBus.emit('apikey:deleted', {
        pluginId: this.pluginId,
        keyName
      });
      
      return true;
    } catch (error) {
      this