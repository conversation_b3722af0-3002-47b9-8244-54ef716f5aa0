/**
 * Plugin IPC Handlers
 *
 * Handles all plugin-related IPC operations between the main process and renderer
 */

const { ipcMain } = require('electron');
const { factory: createLogger } = require('../../core/logger/CoreLogger');

class PluginIpcHandlers {
  /**
   * Initialize plugin IPC handlers
   *
   * @param {Object} pluginManager - The plugin manager instance
   * @param {Object} options - Configuration options
   */
  constructor(pluginManager, options = {}) {
    this.pluginManager = pluginManager;
    this.log = options.logDir ?
      new (require('../../core/logger/CoreLogger').CoreLogger)({
        component: 'plugin-ipc-handlers',
        logDir: options.logDir,
        setupProcessHandlers: false
      }) :
      createLogger('plugin-ipc-handlers');

    this.setupHandlers();
  }

  /**
   * Set up all plugin-related IPC handlers
   */
  setupHandlers() {
    this.log.TRACE('Setting up plugin IPC handlers');

    // Basic plugin management
    this.setupBasicHandlers();

    // Plugin state management
    this.setupStateHandlers();

    // Plugin settings management
    this.setupSettingsHandlers();

    // Plugin registry management
    this.setupRegistryHandlers();

    this.log.TRACE('Plugin IPC handlers setup complete');
  }

  /**
   * Setup basic plugin management handlers
   */
  setupBasicHandlers() {
    ipcMain.handle('plugins:list', async () => {
      this.log.TRACE('IPC: Plugin list request');
      try {
        const result = this.pluginManager ? this.pluginManager.listPlugins() : [];
        this.log.TRACE('IPC: Plugin list returned', { count: result.length });
        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin list failed', { error: error.message });
        return [];
      }
    });

    ipcMain.handle('plugins:enable', async (event, pluginId) => {
      const corrId = `plugin-enable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      this.log.TRACE('IPC: Plugin enable request', { pluginId, corrId });

      try {
        const result = this.pluginManager ? this.pluginManager.enablePlugin(pluginId) : false;

        if (result) {
          this.log.TRACE('Plugin lifecycle: Plugin enabled', {
            pluginId,
            action: 'enable',
            corrId
          });
        } else {
          this.log.TRACE('IPC: Plugin enable returned false', { pluginId, corrId });
        }

        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin enable error', {
          pluginId,
          error: error.message,
          corrId
        });
        return false;
      }
    });

    ipcMain.handle('plugins:disable', async (event, pluginId) => {
      const corrId = `plugin-disable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      this.log.TRACE('IPC: Plugin disable request', { pluginId, corrId });

      try {
        const result = this.pluginManager ? this.pluginManager.disablePlugin(pluginId) : false;

        if (result) {
          this.log.TRACE('Plugin lifecycle: Plugin disabled', {
            pluginId,
            action: 'disable',
            corrId
          });
        } else {
          this.log.TRACE('IPC: Plugin disable returned false', { pluginId, corrId });
        }

        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin disable error', {
          pluginId,
          error: error.message,
          corrId
        });
        return false;
      }
    });

    ipcMain.handle('plugins:reload', async (event, pluginId) => {
      const corrId = `plugin-reload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      this.log.TRACE('IPC: Plugin reload request', { pluginId, corrId });

      try {
        const result = this.pluginManager ? this.pluginManager.reloadPlugin(pluginId) : false;

        if (result) {
          this.log.TRACE('Plugin lifecycle: Plugin reloaded', {
            pluginId,
            action: 'reload',
            corrId
          });
        } else {
          this.log.TRACE('IPC: Plugin reload returned false', { pluginId, corrId });
        }

        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin reload error', {
          pluginId,
          error: error.message,
          corrId
        });
        return false;
      }
    });
  }

  /**
   * Setup plugin state management handlers
   */
  setupStateHandlers() {
    ipcMain.handle('plugins:get-states', async () => {
      return this.pluginManager ? this.pluginManager.getStateManager()?.getAllPluginStates() : [];
    });

    ipcMain.handle('plugins:get-info', async (event, pluginId) => {
      return this.pluginManager ? this.pluginManager.getPluginInfo(pluginId) : null;
    });
  }

  /**
   * Setup plugin settings management handlers
   */
  setupSettingsHandlers() {
    ipcMain.handle('settings:load', async (event, pluginId, schema) => {
      const corrId = `settings-load-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      this.log.TRACE('IPC: Plugin settings load request', {
        pluginId,
        hasSchema: !!schema,
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.getSettingsManager().load(pluginId, schema) : {};

        this.log.TRACE('IPC: Plugin settings loaded successfully', {
          pluginId,
          settingsKeys: Object.keys(result).filter(key => key !== 'apiKey'),
          hasApiKey: !!result.apiKey,
          sensitiveData: false, // Indicate we're not logging sensitive data
          corrId
        });

        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin settings load failed', {
          pluginId,
          error: error.message,
          corrId
        });
        return {};
      }
    });

    ipcMain.handle('settings:save', async (event, pluginId, settings, schema, options) => {
      const corrId = `settings-save-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const validateOnly = options?.validateOnly || false;
      const immediateStorage = options?.immediateStorage || false;

      // ROBUST DEBUG LOGGING - Write to file and console
      const debugMessage = `🚨🚨🚨 CRITICAL IPC: Plugin settings save request received for ${pluginId}`;
      console.log(debugMessage);

      // Write to debug file
      try {
        const fs = require('fs');
        const path = require('path');
        const debugFile = path.join(__dirname, '../../../debug_limitless.log');
        const timestamp = new Date().toISOString();
        fs.appendFileSync(debugFile, `[${timestamp}] ${debugMessage}\n`);
      } catch (e) {
        console.error('Failed to write IPC debug log:', e);
      }

      console.log('🚨🚨🚨 CRITICAL IPC: Plugin settings save request received', {
        pluginId,
        settingsKeys: Object.keys(settings).filter(key => key !== 'apiKey'),
        hasApiKey: !!settings.apiKey,
        apiKeyFirst4: settings.apiKey?.substring(0, 4) || 'none',
        apiKeyLength: settings.apiKey?.length || 0,
        apiKeyTrimmed: settings.apiKey?.trim(),
        apiKeyTrimmedLength: settings.apiKey?.trim()?.length || 0,
        apiKeyEmpty: !settings.apiKey,
        apiKeyEmptyString: settings.apiKey === '',
        hasSchema: !!schema,
        validateOnly,
        immediateStorage,
        options,
        corrId
      });

      this.log.INFO('IPC: Plugin settings save request', {
        pluginId,
        settingsKeys: Object.keys(settings).filter(key => key !== 'apiKey'),
        hasApiKey: !!settings.apiKey,
        hasSchema: !!schema,
        validateOnly,
        immediateStorage,
        corrId
      });

      try {
        if (validateOnly) {
          console.log('🔍 DEBUG IPC: Validation-only mode (manual test), calling plugin manager');
          // Manual validation mode for "Test Connection" functionality
          const result = this.pluginManager ?
            await this.pluginManager.savePluginSettings(pluginId, settings, { validateOnly: true }) :
            { success: false, error: 'Plugin manager not available', validated: true };

          console.log('🔍 DEBUG IPC: Plugin manager validation result:', result);

          this.log.INFO('IPC: Plugin settings validation completed', {
            pluginId,
            success: result.success,
            validated: result.validated,
            hasError: !!result.error,
            corrId
          });

          // Enhanced validation response structure
          const response = {
            success: result.success,
            error: result.error,
            validated: true,
            pluginCanBeEnabled: result.success,
            corrId
          };

          console.log('🔍 DEBUG IPC: Returning validation response:', response);
          return response;

        } else if (immediateStorage) {
          console.log('🔍 DEBUG IPC: Immediate storage mode (Phase 1), calling plugin manager');
          // Phase 1: Immediate storage without network validation
          const result = this.pluginManager ?
            await this.pluginManager.savePluginSettings(pluginId, settings, { immediateStorage: true }) :
            { success: false, error: 'Plugin manager not available' };

          console.log('🔍 DEBUG IPC: Plugin manager immediate storage result:', result);

          this.log.INFO('IPC: Plugin settings immediate storage completed', {
            pluginId,
            success: result.success,
            hasError: !!result.error,
            corrId
          });

          // Return success for Phase 1 completion
          const response = {
            success: result.success,
            error: result.error,
            validationPending: true,
            corrId
          };

          console.log('🔍 DEBUG IPC: Returning immediate storage response:', response);
          return response;

        } else {
          console.log('🔍 DEBUG IPC: Normal save mode, calling plugin manager');
          // Enhanced normal save mode with improved logging
          const result = this.pluginManager ? await this.pluginManager.savePluginSettings(pluginId, settings) : false;

          console.log('🔍 DEBUG IPC: Plugin manager save result:', result);

          if (result) {
            this.log.INFO('IPC: Plugin settings saved successfully', {
              pluginId,
              corrId
            });
          } else {
            this.log.WARN('IPC: Plugin settings save returned false', {
              pluginId,
              corrId
            });
          }

          console.log('🔍 DEBUG IPC: Returning save result:', result);
          return result;
        }
      } catch (error) {
        this.log.ERROR('IPC: Plugin settings save failed', {
          pluginId,
          error: error.message,
          validateOnly,
          immediateStorage,
          corrId
        });
        return validateOnly ?
          { success: false, error: error.message, validated: true } :
          false;
      }
    });

    // New IPC endpoint for triggering background validation
    ipcMain.handle('settings:validate-background', async (event, pluginId, correlationId) => {
      const corrId = correlationId || `bg-validation-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      console.log('🔍 DEBUG IPC: Background validation request received', {
        pluginId,
        correlationId: corrId
      });

      this.log.INFO('IPC: Background validation request', {
        pluginId,
        correlationId: corrId
      });

      try {
        // Trigger background validation via plugin manager
        const result = this.pluginManager ?
          await this.pluginManager.triggerBackgroundValidation(pluginId, corrId) :
          { success: false, error: 'Plugin manager not available' };

        this.log.INFO('IPC: Background validation triggered', {
          pluginId,
          success: result.success,
          correlationId: corrId
        });

        return result;

      } catch (error) {
        this.log.ERROR('IPC: Background validation trigger failed', {
          pluginId,
          error: error.message,
          correlationId: corrId
        });
        return { success: false, error: error.message };
      }
    });

    // New IPC endpoint for querying validation status
    ipcMain.handle('settings:validation-status', async (event, pluginId) => {
      const corrId = `validation-status-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      this.log.INFO('IPC: Validation status request', {
        pluginId,
        corrId
      });

      try {
        const result = this.pluginManager ?
          await this.pluginManager.getValidationStatus(pluginId) :
          { success: false, error: 'Plugin manager not available' };

        return result;

      } catch (error) {
        this.log.ERROR('IPC: Validation status query failed', {
          pluginId,
          error: error.message,
          corrId
        });
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('settings:reset', async (event, pluginId) => {
      const corrId = `settings-reset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      this.log.INFO('IPC: Plugin settings reset request', {
        pluginId,
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.resetPluginSettings(pluginId) : false;

        this.log.INFO('IPC: Plugin settings reset completed', {
          pluginId,
          success: result,
          corrId
        });

        return result;
      } catch (error) {
        this.log.ERROR('IPC: Plugin settings reset failed', {
          pluginId,
          error: error.message,
          corrId
        });
        return false;
      }
    });

    ipcMain.handle('settings:show-modal', async (event, pluginId) => {
      return this.pluginManager ? this.pluginManager.showPluginSettings(pluginId) : null;
    });
  }

  /**
   * Setup plugin registry management handlers
   */
  setupRegistryHandlers() {
    ipcMain.handle('registry:get-all', async () => {
      return this.pluginManager ? this.pluginManager.getPluginRegistry()?.getAllPlugins() : [];
    });

    ipcMain.handle('registry:get-stats', async () => {
      return this.pluginManager ? this.pluginManager.getPluginRegistry()?.getStatistics() : {};
    });

    ipcMain.handle('registry:get-preferences', async () => {
      return this.pluginManager ? this.pluginManager.getPluginRegistry()?.getPreferences() : {};
    });

    ipcMain.handle('registry:update-preferences', async (event, preferences) => {
      return this.pluginManager ? this.pluginManager.getPluginRegistry()?.updatePreferences(preferences) : false;
    });
  }
}

module.exports = { PluginIpcHandlers };
