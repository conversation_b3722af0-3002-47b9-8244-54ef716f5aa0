/**
 * StateManager.js
 * Manages plugin lifecycle states with persistence and validation
 *
 * Handles plugin states: discovered, installing, installed, enabled, disabled,
 * updating, error, uninstalling
 */

const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');
const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * StateManager handles plugin lifecycle states with persistence
 * Manages state files located in $APPDATA/lifeboard/plugins/<id>/state.json
 */
class StateManager extends EventEmitter {
  constructor(baseDirectory, logDir) {
    super();
    this.baseDir = baseDirectory;
    this.stateCache = new Map();
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'StateManager', 
        logDir: logDir, 
        setupProcessHandlers: false,
        logLevel: 'TRACE' // Ensure we capture all logs
      });
    } else {
      this.log = createLogger('StateManager');
    }
    
    this.stateTransitions = this.initializeStateTransitions();
    this.initializeLogger();

    this.log.TRACE('StateManager initialization started', {
      baseDir: this.baseDir,
      cacheSize: this.stateCache.size,
      stateTransitionsCount: this.stateTransitions.size,
      nodeVersion: process.version,
      platform: process.platform,
      memoryUsage: process.memoryUsage()
    });

    this.log.INFO('StateManager initialized', {
      baseDir: this.baseDir,
      stateTransitionsCount: this.stateTransitions.size,
      cacheSize: this.stateCache.size
    });
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    this.log.INFO('StateManager logger initialized', {
      component: 'StateManager',
      baseDir: this.baseDir
    });
  }

  /**
   * Initialize valid state transitions
   * @returns {Map} Map of valid state transitions
   * @private
   */
  initializeStateTransitions() {
    const transitions = new Map([
      ['discovered', ['installing']],
      ['installing', ['installed', 'error']],
      ['installed', ['enabled', 'uninstalling']],
      ['enabled', ['disabled', 'updating', 'uninstalling']],
      ['disabled', ['enabled', 'uninstalling']],
      ['updating', ['enabled', 'error']],
      ['error', ['disabled', 'uninstalling']],
      ['uninstalling', ['discovered']]
    ]);

    this.log.DEBUG('Initialized state transitions', {
      transitionCount: transitions.size
    });
    return transitions;
  }

  /**
   * Get the state file path for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {string} The full path to the state file
   * @private
   */
  getStateFilePath(pluginId) {
    if (!pluginId || typeof pluginId !== 'string') {
      throw new Error('Plugin ID must be a non-empty string');
    }

    const pluginDir = path.join(this.baseDir, pluginId);
    const statePath = path.join(pluginDir, 'state.json');
    this.log.DEBUG('State file path for plugin', {
      pluginId,
      statePath
    });
    return statePath;
  }

  /**
   * Ensure the plugin directory exists
   * @param {string} pluginId - The plugin identifier
   * @private
   */
  ensurePluginDirectory(pluginId) {
    const pluginDir = path.join(this.baseDir, pluginId);
    if (!fs.existsSync(pluginDir)) {
      fs.mkdirSync(pluginDir, { recursive: true });
      this.log.INFO('Created plugin directory', {
        pluginId,
        pluginDir
      });
    }
  }

  /**
   * Create default state object for a plugin
   * @param {string} pluginId - The plugin identifier
   * @param {string} version - The plugin version
   * @param {string} initialState - The initial state
   * @returns {object} Default state object
   * @private
   */
  createDefaultState(pluginId, version, initialState = 'discovered') {
    const now = new Date().toISOString();

    return {
      pluginId,
      version,
      state: initialState,
      lastEnabled: null,
      lastDisabled: null,
      enabledCount: 0,
      createdAt: now,
      lastModified: now,
      statistics: {
        totalCommands: 0,
        totalEvents: 0,
        lastActivity: null
      },
      permissions: {
        granted: [],
        requested: [],
        denied: []
      }
    };
  }

  /**
   * Validate state transition
   * @param {string} currentState - Current plugin state
   * @param {string} newState - Desired new state
   * @returns {boolean} True if transition is valid
   * @private
   */
  isValidTransition(currentState, newState) {
    const validTransitions = this.stateTransitions.get(currentState);
    if (!validTransitions) {
      this.log.ERROR(`[StateManager] Unknown current state: ${currentState}`, {
        component: 'StateManager',
        currentState: currentState
      });
      return false;
    }

    const isValid = validTransitions.includes(newState);
    this.log.DEBUG(`[StateManager] State transition ${currentState} -> ${newState}: ${isValid ? 'valid' : 'invalid'}`, {
      component: 'StateManager',
      currentState: currentState,
      newState: newState,
      isValid: isValid
    });
    return isValid;
  }

  /**
   * Load plugin state from file
   * @param {string} pluginId - The plugin identifier
   * @returns {object} The plugin state object
   */
  getPluginState(pluginId) {
    const traceId = `trace-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.log.TRACE(`[StateManager] Getting state for plugin`, {
      component: 'StateManager',
      pluginId: pluginId,
      traceId,
      cacheHasKey: this.stateCache.has(pluginId),
      cacheSize: this.stateCache.size,
      stack: new Error().stack
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      // Check cache first
      if (this.stateCache.has(pluginId)) {
        const cachedState = this.stateCache.get(pluginId);
        this.log.TRACE('Retrieved state from cache', {
          component: 'StateManager',
          pluginId: pluginId,
          state: cachedState?.state,
          traceId
        });
        return cachedState;
      }

      const statePath = this.getStateFilePath(pluginId);
      let state = null;

      // Check if state file exists
      if (fs.existsSync(statePath)) {
        try {
          const fileContent = fs.readFileSync(statePath, 'utf8');
          
          if (fileContent.trim()) {
            state = JSON.parse(fileContent);
            this.log.TRACE('Successfully parsed state file', {
              component: 'StateManager',
              pluginId: pluginId,
              state: state.state,
              statePath,
              traceId
            });
            
            // Cache the loaded state
            this.stateCache.set(pluginId, state);
            return state;
          } else {
            throw new Error('State file is empty');
          }
        } catch (parseError) {
          this.log.ERROR('Failed to parse state file', {
            component: 'StateManager',
            pluginId: pluginId,
            statePath: statePath,
            error: parseError.message,
            stack: parseError.stack,
            traceId
          });
          throw new Error(`Malformed JSON in state file: ${parseError.message}`);
        }
      }

      // If no state file exists, create a default state
      const defaultState = this.createDefaultState(pluginId, '0.0.0', 'disabled');
      this.log.INFO('Created default state for new plugin', {
        component: 'StateManager',
        pluginId: pluginId,
        state: defaultState.state,
        traceId
      });
      
      // Cache and return the default state
      this.stateCache.set(pluginId, defaultState);
      return defaultState;
      
    } catch (error) {
      const errorContext = {
        component: 'StateManager',
        pluginId: pluginId,
        error: {
          name: error.name,
          message: error.message,
          code: error.code,
          stack: error.stack
        },
        traceId,
        timestamp: new Date().toISOString(),
        cacheState: {
          hasPlugin: this.stateCache.has(pluginId),
          cacheSize: this.stateCache.size
        }
      };
      
      this.log.ERROR('Failed to get plugin state', errorContext);
      
      // Emit error event for any listeners
      this.emit('error', {
        type: 'get_state_failed',
        pluginId,
        error: error.message,
        timestamp: new Date().toISOString(),
        traceId
      });
      
      throw error;
    }
  }

  /**
   * Get all plugin states
   * @returns {Array} Array of all plugin states
   */
  getAllPluginStates() {
    this.log.DEBUG('[StateManager] Getting all plugin states', {
      component: 'StateManager'
    });

    try {
      const states = [];

      // Read all plugin directories
      if (fs.existsSync(this.baseDir)) {
        const pluginDirs = fs.readdirSync(this.baseDir, { withFileTypes: true });

        pluginDirs.forEach(dir => {
          if (dir.isDirectory()) {
            try {
              const state = this.getPluginState(dir.name);
              states.push(state);
            } catch (error) {
              this.log.ERROR(`[StateManager] Failed to load state for plugin ${dir.name}`, {
                component: 'StateManager',
                pluginId: dir.name,
                error: error.message,
                stack: error.stack
              });
            }
          }
        });
      }

      this.log.DEBUG(`[StateManager] Retrieved ${states.length} plugin states`, {
        component: 'StateManager',
        stateCount: states.length
      });
      return states;
    } catch (error) {
      this.log.ERROR('[StateManager] Failed to get all plugin states', {
        component: 'StateManager',
        error: error.message,
        stack: error.stack
      });
      return [];
    }
  }

  /**
   * Update plugin statistics
   * @param {string} pluginId - The plugin identifier
   * @param {object} stats - Statistics to update
   * @returns {boolean} True if updated successfully
   */
  updateStatistics(pluginId, stats) {
    this.log.DEBUG(`[StateManager] Updating statistics for plugin: ${pluginId}`, { pluginId });

    try {
      const currentState = this.getPluginState(pluginId);
      const updatedStats = {
        ...currentState.statistics,
        ...stats,
        lastActivity: new Date().toISOString()
      };

      return this.setPluginState(pluginId, { statistics: updatedStats });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to update statistics for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Update plugin permissions
   * @param {string} pluginId - The plugin identifier
   * @param {object} permissions - Permissions to update
   * @returns {boolean} True if updated successfully
   */
  updatePermissions(pluginId, permissions) {
    this.log.DEBUG(`[StateManager] Updating permissions for plugin: ${pluginId}`, { pluginId });

    try {
      const currentState = this.getPluginState(pluginId);
      const updatedPermissions = {
        ...currentState.permissions,
        ...permissions
      };

      return this.setPluginState(pluginId, { permissions: updatedPermissions });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to update permissions for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Clear cached state for a plugin
   * @param {string} pluginId - The plugin identifier
   */
  clearCache(pluginId) {
    this.log.DEBUG(`[StateManager] Clearing cache for plugin: ${pluginId}`, { pluginId });
    this.stateCache.delete(pluginId);
  }

  /**
   * Clear all cached states
   */
  clearAllCache() {
    this.log.DEBUG('[StateManager] Clearing all state cache', { cacheSize: this.stateCache.size });
    this.stateCache.clear();
  }

  /**
   * Get the current cache size
   * @returns {number} The number of cached states
   */
  getCacheSize() {
    return this.stateCache.size;
  }

  /**
   * Check if state file exists for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if state file exists
   */
  hasState(pluginId) {
    try {
      const statePath = this.getStateFilePath(pluginId);
      return fs.existsSync(statePath);
    } catch (error) {
      this.log.ERROR(`[StateManager] Error checking state existence for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Delete state file for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if deleted successfully
   */
  deleteState(pluginId) {
    this.log.INFO('Deleting state for plugin', {
      pluginId,
      currentState: this.stateCache.get(pluginId)
    });

    try {
      const statePath = this.getStateFilePath(pluginId);

      if (fs.existsSync(statePath)) {
        fs.unlinkSync(statePath);
        this.log.INFO('Successfully deleted state file', {
          pluginId,
          statePath
        });
      }

      // Clear from cache
      this.clearCache(pluginId);

      return true;
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to delete state for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
}

module.exports = StateManager;
