/**
 * PluginManagementUI.js
 * User interface for plugin management with enable/disable functionality
 *
 * Provides plugin cards, filtering, and management controls
 */

const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * PluginManagementUI provides a comprehensive plugin management interface
 * Features filtering, state management, and plugin configuration
 */
class PluginManagementUI {
  constructor(logDir) {
    this.plugins = new Map();
    this.filter = 'all';
    this.searchQuery = '';
    this.notifyRenderer = null;
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'PluginManagementUI', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('PluginManagementUI');
    }
    this.initializeLogger();

    this.log.INFO('PluginManagementUI initialized', {
      maxUIElements: this.maxUIElements
    });
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    const logDir = require('path').join(process.cwd(), 'logs');
    const fs = require('fs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    this.log.INFO('PluginManagementUI logger initialized', {
      component: 'PluginManagementUI'
    });
  }

  /**
   * Set notification function for renderer process
   * @param {Function} notifyFn - Function to notify renderer
   */
  setNotifyRenderer(notifyFn) {
    this.notifyRenderer = notifyFn;
    this.log.DEBUG('[PluginManagementUI] Renderer notification function set', {
      component: 'PluginManagementUI',
      correlationId: this.correlationId
    });
  }

  /**
   * Update plugin data in the UI
   * @param {Array} pluginList - List of plugins with state and registry data
   */
  updatePlugins(pluginList) {
    this.log.DEBUG(`[PluginManagementUI] Updating plugin list with ${pluginList.length} plugins`, {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      pluginCount: pluginList.length
    });

    try {
      this.plugins.clear();

      pluginList.forEach(plugin => {
        if (plugin && plugin.id) {
          this.plugins.set(plugin.id, plugin);
        }
      });

      // Notify renderer of plugin list update
      if (this.notifyRenderer) {
        this.notifyRenderer('plugin-management:plugins-updated', {
          plugins: this.getFilteredPlugins(),
          totalCount: this.plugins.size,
          filter: this.filter,
          timestamp: new Date().toISOString()
        });
      }

    this.log.INFO('Updated plugin list', {
        pluginCount: this.plugins.size,
        totalPlugins: pluginList.length
      });
    } catch (error) {
     this.log.ERROR('Failed to update plugins', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Set filter for plugin display
   * @param {string} filter - Filter type ('all', 'enabled', 'disabled', 'error')
   */
  setFilter(filter) {
    this.log.DEBUG(`[PluginManagementUI] Setting filter to: ${filter}`, {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      filter: filter
    });

    const validFilters = ['all', 'enabled', 'disabled', 'error', 'installing', 'updating'];
    if (!validFilters.includes(filter)) {
     this.log.WARN(`[PluginManagementUI] Invalid filter: ${filter}, using 'all'`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        invalidFilter: filter
      });
      filter = 'all';
    }

    this.filter = filter;

    // Notify renderer of filter change
    if (this.notifyRenderer) {
      this.notifyRenderer('plugin-management:filter-changed', {
        filter: this.filter,
        plugins: this.getFilteredPlugins(),
        timestamp: new Date().toISOString()
      });
    }

   this.log.INFO('Filter set', {
      filter,
      previousFilter: this.currentFilter
    });
  }

  /**
   * Set search query for plugin filtering
   * @param {string} query - Search query string
   */
  setSearchQuery(query) {
   this.log.DEBUG(`[PluginManagementUI] Setting search query: ${query}`, {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      searchQuery: query
    });

    this.searchQuery = (query || '').toLowerCase().trim();

    // Notify renderer of search update
    if (this.notifyRenderer) {
      this.notifyRenderer('plugin-management:search-updated', {
        query: this.searchQuery,
        plugins: this.getFilteredPlugins(),
        timestamp: new Date().toISOString()
      });
    }

   this.log.DEBUG(`[PluginManagementUI] Search query set to: "${this.searchQuery}"`, {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      searchQuery: this.searchQuery
    });
  }

  /**
   * Get filtered plugins based on current filter and search
   * @returns {Array} Filtered plugin list
   */
  getFilteredPlugins() {
    try {
      let plugins = Array.from(this.plugins.values());

      // Apply state filter
      if (this.filter !== 'all') {
        plugins = plugins.filter(plugin => {
          const state = plugin.state || 'unknown';
          return state === this.filter;
        });
      }

      // Apply search filter
      if (this.searchQuery) {
        plugins = plugins.filter(plugin => {
          const searchText = [
            plugin.name || '',
            plugin.id || '',
            plugin.description || '',
            (plugin.tags || []).join(' ')
          ].join(' ').toLowerCase();

          return searchText.includes(this.searchQuery);
        });
      }

      // Sort by name
      plugins.sort((a, b) => {
        const nameA = (a.name || '').toLowerCase();
        const nameB = (b.name || '').toLowerCase();
        return nameA.localeCompare(nameB);
      });

     this.log.DEBUG(`[PluginManagementUI] Filtered ${plugins.length} plugins from ${this.plugins.size} total`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        filteredCount: plugins.length,
        totalCount: this.plugins.size
      });
      return plugins;
    } catch (error) {
     this.log.ERROR('[PluginManagementUI] Failed to filter plugins', {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        error: error.message,
        stack: error.stack
      });
      return [];
    }
  }

  /**
   * Get plugin display data for a specific plugin
   * @param {string} pluginId - Plugin identifier
   * @returns {object|null} Plugin display data
   */
  getPluginDisplayData(pluginId) {
   this.log.DEBUG(`[PluginManagementUI] Getting display data for plugin: ${pluginId}`, {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      pluginId: pluginId
    });

    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
       this.log.WARN(`[PluginManagementUI] Plugin not found: ${pluginId}`, {
          component: 'PluginManagementUI',
          correlationId: this.correlationId,
          pluginId: pluginId
        });
        return null;
      }

      const displayData = {
        id: plugin.id,
        name: plugin.name || plugin.id,
        version: plugin.version || '0.0.0',
        description: plugin.description || 'No description available',
        state: plugin.state || 'unknown',
        enabled: plugin.state === 'enabled',
        permissions: plugin.permissions || [],
        lastActivity: plugin.lastActivity || null,
        lastEnabled: plugin.lastEnabled || null,
        lastDisabled: plugin.lastDisabled || null,
        enabledCount: plugin.enabledCount || 0,
        statistics: plugin.statistics || {
          totalCommands: 0,
          totalEvents: 0,
          lastActivity: null
        },
        error: plugin.error || null,
        installDate: plugin.installDate || null,
        updateAvailable: false, // TODO: Implement update checking
        icon: this.getPluginIcon(plugin),
        tags: plugin.tags || []
      };

     this.log.DEBUG(`[PluginManagementUI] Generated display data for plugin: ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId
      });
      return displayData;
    } catch (error) {
     this.log.ERROR(`[PluginManagementUI] Failed to get display data for plugin ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return null;
    }
  }

  /**
   * Get appropriate icon for plugin based on state
   * @param {object} plugin - Plugin object
   * @returns {string} Icon identifier
   * @private
   */
  getPluginIcon(plugin) {
    if (plugin.icon) {
      return plugin.icon;
    }

    // Default icons based on state
    switch (plugin.state) {
      case 'enabled':
        return 'check-circle';
      case 'disabled':
        return 'circle';
      case 'error':
        return 'alert-circle';
      case 'installing':
      case 'updating':
        return 'clock';
      default:
        return 'package';
    }
  }

  /**
   * Handle plugin state toggle request
   * @param {string} pluginId - Plugin identifier
   * @returns {object} Result of toggle operation
   */
  handleStateToggle(pluginId) {
   this.log.INFO('Handling state toggle for plugin', {
      pluginId
    });

    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        const error = `Plugin not found: ${pluginId}`;
       this.log.ERROR(`[PluginManagementUI] ${error}`, { error, pluginId });
        return { success: false, error };
      }

      const currentState = plugin.state || 'unknown';
      let targetState;

      // Determine target state based on current state
      switch (currentState) {
        case 'enabled':
          targetState = 'disabled';
          break;
        case 'disabled':
        case 'installed':
          targetState = 'enabled';
          break;
        default:
          const error = `Cannot toggle plugin in state: ${currentState}`;
         this.log.WARN(`[PluginManagementUI] ${error}`, {
            component: 'PluginManagementUI',
            correlationId: this.correlationId,
            pluginId: pluginId,
            currentState: currentState,
            error: error
          });
          return { success: false, error };
      }

      // Notify renderer of toggle request (will be handled by plugin manager)
      if (this.notifyRenderer) {
        this.notifyRenderer('plugin-management:state-toggle-requested', {
          pluginId,
          currentState,
          targetState,
          timestamp: new Date().toISOString()
        });
      }

     this.log.INFO('Requested state toggle', {
        pluginId,
        currentState,
        targetState
      });
      return {
        success: true,
        pluginId,
        currentState,
        targetState
      };
    } catch (error) {
     this.log.ERROR(`[PluginManagementUI] Failed to handle state toggle for ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle plugin settings request
   * @param {string} pluginId - Plugin identifier
   * @returns {object} Result of settings request
   */
  handleSettingsRequest(pluginId) {
   this.log.INFO('Handling settings request for plugin', {
      pluginId
    });

    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        const error = `Plugin not found: ${pluginId}`;
       this.log.ERROR(`[PluginManagementUI] ${error}`, {
          component: 'PluginManagementUI',
          correlationId: this.correlationId,
          pluginId: pluginId,
          error: error
        });
        return { success: false, error };
      }

      // Notify renderer of settings request
      if (this.notifyRenderer) {
        this.notifyRenderer('plugin-management:settings-requested', {
          pluginId,
          plugin: this.getPluginDisplayData(pluginId),
          timestamp: new Date().toISOString()
        });
      }

     this.log.INFO('Requested settings for plugin', {
        pluginId
      });
      return { success: true, pluginId };
    } catch (error) {
     this.log.ERROR(`[PluginManagementUI] Failed to handle settings request for ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }



  /**
   * Handle plugin reload request
   * @param {string} pluginId - Plugin identifier
   * @returns {object} Result of reload request
   */
  handleReloadRequest(pluginId) {
   this.log.INFO('Handling reload request for plugin', {
      component: 'PluginManagementUI',
      correlationId: this.correlationId,
      pluginId: pluginId
    });

    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        const error = `Plugin not found: ${pluginId}`;
       this.log.ERROR(`[PluginManagementUI] ${error}`, {
          component: 'PluginManagementUI',
          correlationId: this.correlationId,
          pluginId: pluginId,
          error: error
        });
        return { success: false, error };
      }

      // Notify renderer of reload request
      if (this.notifyRenderer) {
        this.notifyRenderer('plugin-management:reload-requested', {
          pluginId,
          timestamp: new Date().toISOString()
        });
      }

     this.log.INFO(`[PluginManagementUI] Requested reload for plugin: ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId
      });
      return { success: true, pluginId };
    } catch (error) {
     this.log.ERROR(`[PluginManagementUI] Failed to handle reload request for ${pluginId}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Get statistics for plugin management
   * @returns {object} Plugin management statistics
   */
  getStatistics() {
   this.log.DEBUG('[PluginManagementUI] Getting plugin management statistics', {
      component: 'PluginManagementUI',
      correlationId: this.correlationId
    });

    try {
      const totalPlugins = this.plugins.size;
      const stateCount = {};
      let enabledCount = 0;
      let errorCount = 0;

      this.plugins.forEach(plugin => {
        const state = plugin.state || 'unknown';
        stateCount[state] = (stateCount[state] || 0) + 1;

        if (state === 'enabled') enabledCount++;
        if (state === 'error') errorCount++;
      });

      const filteredCount = this.getFilteredPlugins().length;

      const stats = {
        totalPlugins,
        enabledCount,
        errorCount,
        stateCount,
        filteredCount,
        currentFilter: this.filter,
        searchQuery: this.searchQuery,
        timestamp: new Date().toISOString()
      };

     this.log.DEBUG(`[PluginManagementUI] Generated statistics: ${totalPlugins} total, ${enabledCount} enabled, ${errorCount} errors`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        totalPlugins: totalPlugins,
        enabledCount: enabledCount,
        errorCount: errorCount
      });
      return stats;
    } catch (error) {
     this.log.ERROR('[PluginManagementUI] Failed to get statistics', {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        error: error.message,
        stack: error.stack
      });
      return {
        totalPlugins: 0,
        enabledCount: 0,
        errorCount: 0,
        stateCount: {},
        filteredCount: 0,
        currentFilter: this.filter,
        searchQuery: this.searchQuery,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get current UI state
   * @returns {object} Current UI state
   */
  getUIState() {
    return {
      totalPlugins: this.plugins.size,
      filteredPlugins: this.getFilteredPlugins().length,
      currentFilter: this.filter,
      searchQuery: this.searchQuery,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Handle bulk operations on plugins
   * @param {string} operation - Operation type ('enable-all', 'disable-all', 'reload-all')
   * @param {Array} pluginIds - Optional list of specific plugin IDs
   * @returns {object} Result of bulk operation
   */
  handleBulkOperation(operation, pluginIds = null) {
   this.log.INFO('Handling bulk operation', {
      operation,
      operationCount: this.lastOperationCount || 0
    });

    try {
      const validOperations = ['enable-all', 'disable-all', 'reload-all'];
      if (!validOperations.includes(operation)) {
        const error = `Invalid bulk operation: ${operation}`;
       this.log.ERROR(`[PluginManagementUI] ${error}`, {
          component: 'PluginManagementUI',
          correlationId: this.correlationId,
          operation: operation,
          error: error
        });
        return { success: false, error };
      }

      // If no specific plugin IDs provided, use filtered plugins
      const targetPlugins = pluginIds || this.getFilteredPlugins().map(p => p.id);

      if (targetPlugins.length === 0) {
        const error = 'No plugins to perform bulk operation on';
       this.log.WARN(`[PluginManagementUI] ${error}`, {
          component: 'PluginManagementUI',
          correlationId: this.correlationId,
          operation: operation,
          error: error
        });
        return { success: false, error };
      }

      // Notify renderer of bulk operation request
      if (this.notifyRenderer) {
        this.notifyRenderer('plugin-management:bulk-operation-requested', {
          operation,
          pluginIds: targetPlugins,
          timestamp: new Date().toISOString()
        });
      }

     this.log.INFO(`[PluginManagementUI] Requested bulk operation ${operation} on ${targetPlugins.length} plugins`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        operation: operation,
        targetPluginCount: targetPlugins.length
      });
      return {
        success: true,
        operation,
        pluginCount: targetPlugins.length,
        pluginIds: targetPlugins
      };
    } catch (error) {
     this.log.ERROR(`[PluginManagementUI] Failed to handle bulk operation ${operation}`, {
        component: 'PluginManagementUI',
        correlationId: this.correlationId,
        operation: operation,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Clear plugin data
   */
  clear() {
   this.log.DEBUG('[PluginManagementUI] Clearing plugin data', {
      component: 'PluginManagementUI',
      correlationId: this.correlationId
    });
    this.plugins.clear();
    this.filter = 'all';
    this.searchQuery = '';

    if (this.notifyRenderer) {
      this.notifyRenderer('plugin-management:cleared', {
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = PluginManagementUI;
