const { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { CoreLogger } = require('../core/logger/CoreLogger');

// Increase the max listeners for process to avoid memory leak warnings in plugin-heavy environments
process.setMaxListeners(50);

// Set log directory to ./logs/electron (relative to project root)
const projectRoot = path.resolve(__dirname, '../..');
const electronLogDir = path.join(projectRoot, 'logs', 'electron');
if (!fs.existsSync(electronLogDir)) {
  fs.mkdirSync(electronLogDir, { recursive: true, mode: 0o700 });
}
const logOptions = { logDir: electronLogDir };
const log = new CoreLogger(logOptions);

// Test log to confirm electron logging is working
log.INFO('Electron application starting', {
  logDir: electronLogDir,
  isDev: process.env.ELECTRON_IS_DEV === 'true'
});

// Security: Disable node integration by default
const isDev = process.env.ELECTRON_IS_DEV === 'true';

class LifeboardApp {
  constructor() {
    this.mainWindow = null;
    this.pluginManager = null;
    this.setupApp();
  }

  setupApp() {
    // Security: Prevent new window creation
    // Note: Temporarily disabled - may need to be updated for current Electron version
    // app.on('web-contents-created', (event, contents) => {
    //   if (contents) {
    //     contents.on('new-window', (event, navigationUrl) => {
    //       event.preventDefault();
    //       shell.openExternal(navigationUrl);
    //     });
    //   }
    // });

    // App event handlers
    app.whenReady().then(() => {
      this.createMainWindow();
      this.initializePluginManager();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Setup IPC handlers
    this.setupIpcHandlers();
  }

  createMainWindow() {
    log.INFO('Creating main window');

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Start hidden, show when ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: !isDev, // Disable in dev for local file access
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    });

    // Load the web UI
    const webUIPath = isDev
      ? 'http://localhost:9820' // Dev: Use running web server
      : `file://${path.join(__dirname, '../webui/index.html')}`; // Prod: Local files

    this.mainWindow.loadURL(webUIPath);

    // Window event handlers
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      log.INFO('Main window shown');
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Development tools
    if (isDev) {
      this.mainWindow.webContents.openDevTools();
    }

    // Security: Prevent navigation to external URLs
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);

      if (parsedUrl.origin !== 'http://localhost:9820' && !navigationUrl.startsWith('file://')) {
        event.preventDefault();
        log.WARN('Blocked navigation attempt', { navigationUrl });
      }
    });
  }

  initializePluginManager() {
    log.INFO('Initializing plugin manager');

    try {
      const { PluginManager } = require('./plugin-manager');
      log.INFO('PluginManager class loaded successfully');

      this.pluginManager = new PluginManager(this, { logDir: electronLogDir });
      log.INFO('PluginManager instance created successfully');

      // Initialize plugin directories
      log.INFO('Starting plugin manager initialization');
      this.pluginManager.initialize();
      log.INFO('Plugin manager initialization completed');

      // Set up renderer notification for UI managers
      log.INFO('Setting up renderer notification');
      this.pluginManager.setNotifyRenderer((channel, data) => {
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send(channel, data);
        }
      });
      log.INFO('Renderer notification setup completed');

    } catch (error) {
      log.ERROR('Error during plugin manager initialization:', {
        error: error.message,
        stack: error.stack,
        phase: 'initialization'
      });
      throw error;
    }
  }

  setupIpcHandlers() {
    const { PluginIpcHandlers } = require('./ipc/PluginIpcHandlers');
    new PluginIpcHandlers(this.pluginManager, { logDir: electronLogDir });

    // Command palette IPC handlers
    ipcMain.handle('commands:list', async () => {
      return this.pluginManager ? this.pluginManager.commandPalette.listCommands() : [];
    });

    ipcMain.handle('commands:execute', async (event, commandId) => {
      if (this.pluginManager) {
        this.pluginManager.commandPalette.executeCommand(commandId);
        return true;
      }
      return false;
    });

    // M4 UI IPC Handlers

    // Ribbon Icon IPC
    ipcMain.handle('ribbon:list', async () => {
      return this.pluginManager ? this.pluginManager.getRibbonManager().getAllRibbonIcons() : [];
    });

    ipcMain.handle('ribbon:click', async (event, iconId) => {
      return this.pluginManager ? this.pluginManager.getRibbonManager().executeIconCallback(iconId) : false;
    });

    // Modal IPC
    ipcMain.handle('modal:list', async () => {
      return this.pluginManager ? this.pluginManager.getModalManager().getOpenModals() : [];
    });

    ipcMain.handle('modal:close', async (event, modalId) => {
      return this.pluginManager ? this.pluginManager.getModalManager().closeModal(modalId) : false;
    });

    ipcMain.handle('modal:button-click', async (event, modalId, buttonId) => {
      return this.pluginManager ? this.pluginManager.getModalManager().handleButtonClick(modalId, buttonId) : false;
    });

    // Command Palette UI IPC
    ipcMain.handle('command-palette:show', async (event, query) => {
      if (this.pluginManager) {
        this.pluginManager.getCommandPaletteUI().show(query);
        return true;
      }
      return false;
    });

    ipcMain.handle('command-palette:hide', async () => {
      if (this.pluginManager) {
        this.pluginManager.getCommandPaletteUI().hide();
        return true;
      }
      return false;
    });

    ipcMain.handle('command-palette:search', async (event, query) => {
      if (this.pluginManager) {
        this.pluginManager.getCommandPaletteUI().updateSearch(query);
        return true;
      }
      return false;
    });

    ipcMain.handle('command-palette:select-next', async () => {
      if (this.pluginManager) {
        this.pluginManager.getCommandPaletteUI().selectNext();
        return true;
      }
      return false;
    });

    ipcMain.handle('command-palette:select-previous', async () => {
      if (this.pluginManager) {
        this.pluginManager.getCommandPaletteUI().selectPrevious();
        return true;
      }
      return false;
    });

    ipcMain.handle('command-palette:execute-selected', async () => {
      return this.pluginManager ? this.pluginManager.getCommandPaletteUI().executeSelected() : false;
    });

    // M5: Plugin Management IPC Handlers
    // Note: Basic plugin management handlers (plugins:*, settings:*) are now handled by PluginIpcHandlers.js
    // This prevents duplicate handler registration errors

    // Plugin registry management handlers are now in PluginIpcHandlers.js

    // Plugin management UI operations
    ipcMain.handle('plugin-management:set-filter', async (event, filter) => {
      if (this.pluginManager) {
        this.pluginManager.getPluginManagementUI().setFilter(filter);
        return true;
      }
      return false;
    });

    ipcMain.handle('plugin-management:set-search', async (event, query) => {
      if (this.pluginManager) {
        this.pluginManager.getPluginManagementUI().setSearchQuery(query);
        return true;
      }
      return false;
    });

    ipcMain.handle('plugin-management:toggle-state', async (event, pluginId) => {
      const corrId = `plugin-toggle-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      log.INFO('IPC: Plugin state toggle request', { pluginId, corrId });

      try {
        const result = this.pluginManager ? this.pluginManager.getPluginManagementUI().handleStateToggle(pluginId) : { success: false };

        log.INFO('Plugin lifecycle: State toggle completed', {
          pluginId,
          success: result.success,
          corrId
        });

        return result;
      } catch (error) {
        log.ERROR('IPC: Plugin state toggle error', {
          pluginId,
          error: error.message,
          corrId
        });
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('plugin-management:show-settings', async (event, pluginId) => {
      const corrId = `plugin-settings-ui-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      log.INFO('User interaction: Plugin settings requested', {
        pluginId,
        action: 'show_settings',
        source: 'plugin_management_ui',
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.getPluginManagementUI().handleSettingsRequest(pluginId) : { success: false };

        log.INFO('Plugin management: Settings UI request handled', {
          pluginId,
          success: result.success,
          corrId
        });

        return result;
      } catch (error) {
        log.ERROR('IPC: Plugin settings UI error', {
          pluginId,
          error: error.message,
          corrId
        });
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('plugin-management:bulk-operation', async (event, operation, pluginIds) => {
      return this.pluginManager ? this.pluginManager.getPluginManagementUI().handleBulkOperation(operation, pluginIds) : { success: false };
    });

    // Settings modal operations
    ipcMain.handle('settings-modal:update', async (event, modalId, settings) => {
      const corrId = `modal-update-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      log.INFO('IPC: Settings modal update request', {
        modalId,
        settingsKeys: Object.keys(settings).filter(key => key !== 'apiKey'),
        hasApiKey: !!settings.apiKey,
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.getSettingsModalUI().updateSettings(modalId, settings) : false;

        log.DEBUG('Settings modal: Update completed', {
          modalId,
          success: result,
          corrId
        });

        return result;
      } catch (error) {
        log.ERROR('IPC: Settings modal update error', {
          modalId,
          error: error.message,
          corrId
        });
        return false;
      }
    });

    ipcMain.handle('settings-modal:save', async (event, modalId) => {
      const corrId = `modal-save-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      log.INFO('🚨 CRITICAL: Settings modal save initiated', {
        modalId,
        action: 'save_settings',
        source: 'settings_modal',
        corrId
      });

      log.INFO('🚨 CRITICAL: User interaction: Settings save initiated', {
        modalId,
        action: 'save_settings',
        source: 'settings_modal',
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.getSettingsModalUI().saveSettings(modalId) : { success: false };

        if (result.success) {
          log.INFO('Configuration change: Settings saved via modal', {
            modalId,
            corrId
          });
        } else {
          log.WARN('Settings modal: Save failed', {
            modalId,
            error: result.error,
            corrId
          });
        }

        return result;
      } catch (error) {
        log.ERROR('IPC: Settings modal save error', {
          modalId,
          error: error.message,
          corrId
        });
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('settings-modal:reset', async (event, modalId) => {
      const corrId = `modal-reset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      log.INFO('User interaction: Settings reset initiated', {
        modalId,
        action: 'reset_settings',
        source: 'settings_modal',
        corrId
      });

      try {
        const result = this.pluginManager ? this.pluginManager.getSettingsModalUI().resetSettings(modalId) : false;

        if (result) {
          log.INFO('Configuration change: Settings reset via modal', {
            modalId,
            corrId
          });
        }

        return result;
      } catch (error) {
        log.ERROR('IPC: Settings modal reset error', {
          modalId,
          error: error.message,
          corrId
        });
        return false;
      }
    });

    ipcMain.handle('settings-modal:close', async (event, modalId) => {
      log.DEBUG('User interaction: Settings modal closed', {
        modalId,
        action: 'close_modal',
        source: 'settings_modal'
      });

      return this.pluginManager ? this.pluginManager.getSettingsModalUI().closeModal(modalId) : false;
    });

    // M6: Marketplace IPC Handlers

    // Marketplace search and discovery
    ipcMain.handle('marketplace:search', async (event, query, filters) => {
      return this.pluginManager ? this.pluginManager.searchMarketplace(query, filters) : [];
    });

    ipcMain.handle('marketplace:get-plugin-details', async (event, pluginId) => {
      return this.pluginManager ? this.pluginManager.getMarketplacePluginDetails(pluginId) : null;
    });

    ipcMain.handle('marketplace:get-stats', async () => {
      return this.pluginManager ? this.pluginManager.getMarketplaceStats() : {};
    });

    // Plugin installation and management
    ipcMain.handle('marketplace:install', async (event, pluginId, options) => {
      return this.pluginManager ? this.pluginManager.installFromMarketplace(pluginId, options) : { success: false, error: 'Plugin manager not available' };
    });



    ipcMain.handle('marketplace:check-updates', async (event, pluginId) => {
      return this.pluginManager ? this.pluginManager.checkForUpdates(pluginId) : [];
    });

    // Package management
    ipcMain.handle('package:create', async (event, sourcePath, options) => {
      return this.pluginManager ? this.pluginManager.createPackage(sourcePath, options) : { success: false, error: 'Plugin manager not available' };
    });

    ipcMain.handle('package:verify', async (event, packagePath, expectedMetadata) => {
      return this.pluginManager ? this.pluginManager.verifyPackage(packagePath, expectedMetadata) : { valid: false, error: 'Plugin manager not available' };
    });

    // Installation status tracking
    ipcMain.handle('marketplace:get-installation-status', async (event, pluginId) => {
      if (this.pluginManager && this.pluginManager.getMarketplaceManager()) {
        return this.pluginManager.getMarketplaceManager().getInstallationStatus(pluginId);
      }
      return null;
    });

    // UI Statistics IPC (M5 Enhanced)
    ipcMain.handle('ui:stats', async () => {
      return this.pluginManager ? this.pluginManager.getUIStats() : {};
    });

    // App info IPC
    ipcMain.handle('app:version', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:platform', () => {
      return process.platform;
    });

    // Logging IPC
    ipcMain.handle('log:debug', (event, message) => {
      log.DEBUG(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:info', (event, message) => {
      log.INFO(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:warn', (event, message) => {
      log.WARN(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:error', (event, message) => {
      log.ERROR(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:fatal', (event, message) => {
      log.FATAL(`[Renderer] ${message}`, { source: 'renderer' });
    });

    // Show native dialogs
    ipcMain.handle('dialog:showError', async (event, title, content) => {
      dialog.showErrorBox(title, content);
    });

    ipcMain.handle('dialog:showMessage', async (event, options) => {
      return dialog.showMessageBox(this.mainWindow, options);
    });
  }

  getMainWindow() {
    return this.mainWindow;
  }

  getPluginManager() {
    return this.pluginManager;
  }
}

// Create and start the app
const lifeboardApp = new LifeboardApp();

// Export for testing
module.exports = LifeboardApp;
