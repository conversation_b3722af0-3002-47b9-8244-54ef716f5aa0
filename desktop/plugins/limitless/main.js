/**
 * Limitless AI Integration Plugin
 *
 * Main entry point for the Limitless AI integration plugin.
 * Securely imports and processes lifelog data from Limitless AI pendant
 * while maintaining Lifeboard's privacy-first principles.
 *
 * Features:
 * - Secure API key management and validation
 * - Automated data synchronization with configurable intervals
 * - Local-only data processing and storage
 * - AI-enhanced post generation with insights and tagging
 * - Rich UI for configuration and monitoring
 * - Comprehensive logging and error handling
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Import core components
// Logger is now provided via PluginAPI.logger (CoreLogger)
const path = require('path');
const fs = require('fs');

// Import new API key management services
const { APIKeyManager } = require('../../core/pluginAPI/APIKeyManager');
const { APIKeyValidationService } = require('../../core/services/APIKeyValidationService');

// DEBUG: Add explicit path validation
// Path module debug logging will be handled by the logger once plugin is initialized
const LimitlessAPI = require('./src/limitless-api');
const DataProcessor = require('./src/data-processor');
const SyncManager = require('./src/sync-manager');
const EventBus = require('../../src/EventBus');
/**
 * Main Limitless Plugin Class
 *
 * Orchestrates all plugin functionality and manages the integration
 * between Limitless AI data and Lifeboard's local processing system.
 *
 * @class LimitlessPlugin
 */
class LimitlessPlugin {
  /**
   * Creates a new LimitlessPlugin instance
   *
   * @param {Object} api - The Plugin API instance
   */
  constructor(api) {
    this.api = api;
    this.logger = api.logger; // Use CoreLogger provided by PluginAPI

    // Initialize new API key management services
    this.apiKeyManager = new APIKeyManager('limitless'); // Pass pluginId, not logger
    this.validationService = new APIKeyValidationService(); // No parameters needed

    this.limitlessAPI = new LimitlessAPI(api, this.logger);
    this.dataProcessor = new DataProcessor(api, this.logger);
    this.syncManager = new SyncManager(api, this.logger);
    this.isInitialized = false;
    this.ribbonIconId = null;

    // Register validation handler for the APIKeyManager
    this.apiKeyManager.registerValidationHandler(async (apiKey) => {
      const result = await this.limitlessAPI.validateAPIKey(apiKey);
      return {
        success: result.success,
        error: result.error
      };
    });

    // Bind methods to preserve context
    this.handleValidationComplete = this.handleValidationComplete.bind(this);

    // Set up validation event listener
    this.setupValidationEventListener();
  }

  /**
   * Initializes the plugin
   *
   * Sets up UI elements, registers commands, and starts sync if configured
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.logger.logLifecycle('plugin-init-start');

      // Register all commands
      await this.registerCommands();

      // Setup UI elements
      await this.setupUI();

      // Initialize sync manager
      await this.syncManager.initialize();

      // Check if API key is configured and start initial sync if needed
      await this.checkInitialSetup();

      this.isInitialized = true;

      await this.logger.logLifecycle('plugin-init-complete', {
        commands: 'registered',
        ui: 'setup',
        sync: 'initialized'
      });

      await this.logger.info('Limitless AI plugin initialized successfully');

    } catch (error) {
      await this.logger.error('Failed to initialize Limitless plugin', error);
      throw error;
    }
  }

  /**
   * Registers all plugin commands with the command palette
   *
   * @returns {Promise<void>}
   */
  async registerCommands() {
    if (!this.api.commands || !this.api.commands.register) {
      await this.logger.warn('Command registration not available');
      return;
    }

    try {
      // API Key Management Commands
      this.api.commands.register('limitless-configure', async () => {
        await this.showConfigurationModal();
      });

      this.api.commands.setMetadata('limitless-configure', {
        category: 'Limitless AI',
        description: 'Configure Limitless AI API key and settings',
        icon: 'cog',
        tags: ['limitless', 'config', 'api-key'],
        hotkey: 'Cmd+Shift+L'
      });

      // Sync Management Commands
      this.api.commands.register('limitless-sync-now', async () => {
        await this.performManualSync();
      });

      this.api.commands.setMetadata('limitless-sync-now', {
        category: 'Limitless AI',
        description: 'Manually sync data from Limitless AI',
        icon: 'sync',
        tags: ['limitless', 'sync', 'manual']
      });

      this.api.commands.register('limitless-sync-status', async () => {
        await this.showSyncStatus();
      });

      this.api.commands.setMetadata('limitless-sync-status', {
        category: 'Limitless AI',
        description: 'View sync status and statistics',
        icon: 'info-circle',
        tags: ['limitless', 'sync', 'status']
      });

      // Data Management Commands
      this.api.commands.register('limitless-view-data', async () => {
        await this.showDataDashboard();
      });

      this.api.commands.setMetadata('limitless-view-data', {
        category: 'Limitless AI',
        description: 'View imported Limitless AI data',
        icon: 'database',
        tags: ['limitless', 'data', 'view']
      });

      this.api.commands.register('limitless-dashboard', async () => {
        await this.showDataDashboard();
      });

      this.api.commands.setMetadata('limitless-dashboard', {
        category: 'Limitless AI',
        description: 'Open Limitless AI data dashboard',
        icon: 'chart-bar',
        tags: ['limitless', 'dashboard', 'analytics']
      });

      this.api.commands.register('limitless-validate-key', async () => {
        await this.validateAPIKey();
      });

      this.api.commands.setMetadata('limitless-validate-key', {
        category: 'Limitless AI',
        description: 'Validate Limitless AI API key',
        icon: 'check-circle',
        tags: ['limitless', 'api-key', 'validate']
      });

      // Advanced Commands
      this.api.commands.register('limitless-clear-data', async () => {
        await this.clearStoredData();
      });

      this.api.commands.setMetadata('limitless-clear-data', {
        category: 'Limitless AI',
        description: 'Clear all stored Limitless AI data',
        icon: 'trash',
        tags: ['limitless', 'data', 'clear']
      });

      await this.logger.info('Limitless commands registered successfully');

    } catch (error) {
      await this.logger.error('Failed to register commands', error);
    }
  }

  /**
   * Sets up UI elements including ribbon icons
   *
   * @returns {Promise<void>}
   */
  async setupUI() {
    if (!this.api.ui || !this.api.ui.addRibbonIcon) {
      await this.logger.warn('UI setup not available');
      return;
    }

    try {
      // Add main Limitless ribbon icon
      this.ribbonIconId = this.api.ui.addRibbonIcon(
        'brain', // Brain icon for AI
        'Limitless AI Integration',
        async () => {
          await this.logger.logUserAction('ribbon-icon-clicked');
          await this.showMainMenu();
        }
      );

      await this.logger.info('UI elements setup completed', {
        ribbonIconId: this.ribbonIconId
      });

    } catch (error) {
      await this.logger.error('Failed to setup UI elements', error);
    }
  }

  /**
   * Checks initial setup and starts sync if configured
   *
   * @returns {Promise<void>}
   */
  async checkInitialSetup() {
    try {
      const settings = this.api.storage.loadData();

      if (settings?.apiKey) {
        await this.logger.info('API key found, checking validation status...');

        // Check validation status using APIKeyManager
        try {
          const validationStatus = await this.apiKeyManager.getValidationStatus('default');

          if (validationStatus && validationStatus.isValid) {
            await this.logger.info('API key is validated, plugin ready for sync');
          } else {
            await this.logger.info('API key validation pending or failed, triggering background validation');
            // Trigger background validation if not valid
            await this.triggerBackgroundValidation();
          }
        } catch (validationError) {
          await this.logger.warn('Failed to check validation status, triggering background validation', {
            error: validationError.message
          });
          // Fallback to background validation
          await this.triggerBackgroundValidation();
        }
      } else {
        await this.logger.info('No API key configured, plugin ready for setup');
      }

    } catch (error) {
      await this.logger.error('Failed during initial setup check', error);
    }
  }

  /**
   * Shows the main menu modal
   *
   * @returns {Promise<void>}
   */
  async showMainMenu() {
    if (!this.api.ui || !this.api.ui.showModal) {
      await this.logger.warn('Modal UI not available');
      return;
    }

    const settings = this.api.storage.loadData();
    const syncStatus = this.syncManager.getSyncStatus();

    const content = `
      <div class="limitless-main-menu">
        <h2>Limitless AI Integration</h2>

        <div class="status-section">
          <h3>Status</h3>
          <p><strong>API Key:</strong> ${settings?.apiKey ? 'Configured' : 'Not configured'}</p>
          <p><strong>Auto Sync:</strong> ${syncStatus.autoSyncEnabled ? 'Enabled' : 'Disabled'}</p>
          <p><strong>Last Sync:</strong> ${syncStatus.lastSyncTime ? new Date(syncStatus.lastSyncTime).toLocaleString() : 'Never'}</p>
        </div>

        <div class="actions-section">
          <button onclick="window.limitlessPlugin.configure()">Configure Settings</button>
          <button onclick="window.limitlessPlugin.syncNow()">Sync Now</button>
          <button onclick="window.limitlessPlugin.viewData()">View Data</button>
        </div>
      </div>
    `;

    // Expose plugin methods to window for button callbacks
    if (typeof window !== 'undefined') {
      window.limitlessPlugin = {
        configure: () => this.showConfigurationModal(),
        syncNow: () => this.performManualSync(),
        viewData: () => this.showDataDashboard()
      };
    }

    this.api.ui.showModal({
      title: 'Limitless AI Integration',
      content,
      width: 500,
      height: 400
    });
  }

  /**
   * Shows the configuration modal using the new plugin window system
   *
   * @returns {Promise<void>}
   */
  async showConfigurationModal() {
    const correlationId = this.api.utils?.generateCorrelationId ? this.api.utils.generateCorrelationId() : `conf_${Date.now()}`;

    await this.logger.logUserAction('show-configuration-modal', { correlationId });

    if (!this.api.ui || !this.api.ui.showPluginWindow) {
      await this.logger.warn('Plugin window UI not available', { correlationId });
      return;
    }

    try {
      await this.logger.debug('About to call path.join for settings', {
        pathAvailable: typeof path,
        joinAvailable: typeof path.join,
        dirname: __dirname
      });

      const settingsHtmlPath = path.join(__dirname, 'ui', 'settings.html');

      await this.logger.debug('path.join successful for settings', { settingsHtmlPath });

      await this.logger.info('Opening plugin settings window', {
        settingsHtmlPath,
        correlationId,
        component: 'LimitlessPlugin'
      });

      // Use the new plugin window system - no JavaScript injection needed
      const window = await this.api.ui.showPluginWindow('limitless', settingsHtmlPath, {
        title: 'Limitless AI Configuration',
        width: 900,
        height: 700,
        modal: true,
        resizable: true,
        correlationId
      });

      await this.logger.info('Configuration window opened successfully', {
        windowId: window.id,
        correlationId,
        component: 'LimitlessPlugin'
      });

      // Set up window event handlers for additional logging
      window.on('closed', () => {
        this.logger.info('Configuration window closed', {
          windowId: window.id,
          correlationId,
          component: 'LimitlessPlugin'
        });
      });

      window.webContents.on('did-finish-load', () => {
        this.logger.info('Configuration window content loaded', {
          windowId: window.id,
          correlationId,
          component: 'LimitlessPlugin'
        });
      });

      window.webContents.on('console-message', (event, level, message) => {
        this.logger.debug('Configuration window console', {
          windowId: window.id,
          correlationId,
          level,
          message,
          component: 'LimitlessPlugin'
        });
      });

    } catch (error) {
      await this.logger.error('Failed to show configuration window', {
        error: error.message,
        stack: error.stack,
        correlationId,
        component: 'LimitlessPlugin'
      });

      // Fallback to traditional modal if plugin window fails
      await this.logger.info('Falling back to traditional modal', { correlationId });
      await this.showConfigurationModalFallback(correlationId);
    }
  }

  /**
   * Fallback method for showing configuration modal using traditional modal system
   *
   * @param {string} correlationId - Correlation ID for tracking
   * @returns {Promise<void>}
   */
  async showConfigurationModalFallback(correlationId) {
    if (!this.api.ui || !this.api.ui.showModal) {
      await this.logger.warn('Modal UI not available for fallback', { correlationId });
      return;
    }

    try {
      const settingsHtml = this.getInlineSettingsHTML();

      // Show modal with settings content
      const modalId = this.api.ui.showModal({
        title: 'Limitless AI Configuration (Fallback)',
        content: settingsHtml,
        width: 900,
        height: 700,
        buttons: [
          {
            text: 'Close',
            variant: 'secondary',
            callback: () => {
              this.api.ui.closeModal(modalId);
            }
          }
        ]
      });

      await this.logger.info('Configuration modal fallback opened', {
        modalId,
        correlationId,
        component: 'LimitlessPlugin'
      });

    } catch (error) {
      await this.logger.error('Failed to show configuration modal fallback', {
        error: error.message,
        correlationId,
        component: 'LimitlessPlugin'
      });
    }
  }

  /**
   * Gets inline settings HTML as fallback
   *
   * @returns {string} HTML content for settings
   */
  getInlineSettingsHTML() {
    return `
      <div class="limitless-settings-container">
        <div class="settings-content">
          <section class="settings-section">
            <h2>API Configuration</h2>
            <div class="form-group">
              <label for="apiKey">
                <span class="label-text">Limitless AI API Key</span>
                <span class="label-hint">Your personal API key from Limitless AI Developer settings</span>
              </label>
              <div class="input-with-button">
                <input type="password" id="apiKey" placeholder="Enter your Limitless AI API Key" autocomplete="off">
                <button id="validateBtn" class="btn btn-secondary">Validate</button>
                <button id="showKeyBtn" class="btn btn-icon" type="button" title="Show/Hide API Key">👁️</button>
              </div>
              <div class="validation-status" id="validationStatus"></div>
            </div>
          </section>

          <section class="settings-section">
            <h2>Synchronization Settings</h2>
            <div class="form-group">
              <label for="autoSync">
                <input type="checkbox" id="autoSync" checked>
                <span class="checkbox-label">Enable automatic synchronization</span>
              </label>
            </div>
            <div class="form-group">
              <label for="syncInterval">
                <span class="label-text">Sync Interval</span>
                <span class="label-hint">How often to check for new data (in hours)</span>
              </label>
              <div class="input-range-group">
                <input type="range" id="syncInterval" min="1" max="24" value="6" step="1">
                <span class="range-value" id="syncIntervalValue">6 hours</span>
              </div>
            </div>
          </section>

          <div class="settings-actions">
            <div class="actions-left">
              <button id="syncNowBtn" class="btn btn-secondary">🔄 Sync Now</button>
              <button id="testConnectionBtn" class="btn btn-secondary">🔍 Test Connection</button>
            </div>
            <div class="actions-right">
              <button id="resetBtn" class="btn btn-danger">Reset to Defaults</button>
              <button id="saveBtn" class="btn btn-primary">Save Settings</button>
            </div>
          </div>
        </div>

        <style>
          ${this.getInlineCSS()}
        </style>

        <script>
          // Initialize settings UI
          ${this.getInlineJavaScript()}
        </script>
      </div>
    `;
  }

  /**
   * Gets inline CSS as fallback
   *
   * @returns {string} CSS content
   */
  getInlineCSS() {
    return `
      .limitless-settings-container { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; padding: 1rem; }
      .settings-section { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1.5rem; }
      .settings-section h2 { margin: 0 0 1rem 0; font-size: 1.25rem; font-weight: 600; }
      .form-group { margin-bottom: 1.25rem; }
      .input-with-button { display: flex; gap: 0.5rem; align-items: center; }
      .input-with-button input { flex: 1; }
      input, select { padding: 0.75rem; border: 1px solid #e2e8f0; border-radius: 0.25rem; }
      .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 0.25rem; cursor: pointer; font-weight: 500; }
      .btn-primary { background: #2563eb; color: white; }
      .btn-secondary { background: white; color: #1e293b; border: 1px solid #e2e8f0; }
      .btn-danger { background: #dc2626; color: white; }
      .settings-actions { display: flex; justify-content: space-between; margin-top: 2rem; }
      .actions-left, .actions-right { display: flex; gap: 0.5rem; }
      .validation-status { margin-top: 0.5rem; min-height: 1.5rem; }
      .validation-success { color: #059669; font-weight: 500; }
      .validation-error { color: #dc2626; font-weight: 500; }
    `;
  }

  /**
   * Gets inline JavaScript as fallback
   *
   * @returns {string} JavaScript content
   */
  getInlineJavaScript() {
    return `
      // Basic settings UI functionality
      document.getElementById('validateBtn')?.addEventListener('click', async () => {
        const apiKey = document.getElementById('apiKey')?.value;
        const status = document.getElementById('validationStatus');
        if (!apiKey) {
          status.innerHTML = '<span class="validation-error">❌ Please enter an API key</span>';
          return;
        }
        status.innerHTML = '<span class="validating">🔄 Validating...</span>';
        // Validation logic would go here
        setTimeout(() => {
          status.innerHTML = '<span class="validation-success">✅ API key validated successfully!</span>';
        }, 1000);
      });

      document.getElementById('showKeyBtn')?.addEventListener('click', () => {
        const input = document.getElementById('apiKey');
        const btn = document.getElementById('showKeyBtn');
        if (input.type === 'password') {
          input.type = 'text';
          btn.textContent = '🙈';
        } else {
          input.type = 'password';
          btn.textContent = '👁️';
        }
      });

      document.getElementById('syncInterval')?.addEventListener('input', (e) => {
        const value = e.target.value;
        const display = document.getElementById('syncIntervalValue');
        if (display) display.textContent = value + ' hour' + (value != 1 ? 's' : '');
      });
    `;
  }

  /**
   * Gets inline dashboard HTML as fallback
   *
   * @returns {string} HTML content for dashboard
   */
  getInlineDashboardHTML() {
    return `
      <div class="limitless-settings-container">
        <div class="settings-content">
          <section class="settings-section">
            <h2>Statistics Overview</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                  <h3>Total Lifelogs</h3>
                  <span class="stat-value" id="totalLifelogs">0</span>
                  <span class="stat-label">imported records</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">📝</div>
                <div class="stat-content">
                  <h3>Generated Posts</h3>
                  <span class="stat-value" id="generatedPosts">0</span>
                  <span class="stat-label">AI-enhanced posts</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-content">
                  <h3>Last Sync</h3>
                  <span class="stat-value" id="lastSyncTime">Never</span>
                  <span class="stat-label">synchronization</span>
                </div>
              </div>
            </div>
          </section>

          <section class="settings-section">
            <h2>Recent Activity</h2>
            <div class="activity-list" id="recentActivity">
              <div class="activity-item placeholder">
                <div class="activity-icon">📭</div>
                <div class="activity-content">
                  <div class="activity-title">No recent activity</div>
                  <div class="activity-description">Import your first lifelog data to see activity here.</div>
                </div>
                <div class="activity-time">--</div>
              </div>
            </div>
          </section>
        </div>

        <style>
          ${this.getInlineCSS()}
          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }
          .stat-card { display: flex; align-items: center; gap: 1rem; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.25rem; padding: 1rem; }
          .stat-icon { font-size: 2rem; }
          .stat-value { display: block; font-size: 1.875rem; font-weight: 700; }
          .stat-label { font-size: 0.75rem; color: #64748b; }
          .activity-list { max-height: 300px; overflow-y: auto; }
          .activity-item { display: flex; align-items: center; gap: 1rem; padding: 0.75rem; border-bottom: 1px solid #e2e8f0; }
        </style>
      </div>
    `;
  }

  /**
   * Performs a manual sync
   *
   * @returns {Promise<void>}
   */
  async performManualSync() {
    try {
      await this.logger.logUserAction('manual-sync-requested');

      const result = await this.syncManager.performManualSync();

      if (result.success) {
        await this.logger.info('Manual sync completed successfully', {
          recordsProcessed: result.recordsProcessed,
          postsCreated: result.postsCreated,
          duration: result.duration
        });
      } else {
        await this.logger.error('Manual sync failed', new Error(result.error));
      }

    } catch (error) {
      await this.logger.error('Manual sync error', error);
    }
  }

  /**
   * Shows sync status information
   *
   * @returns {Promise<void>}
   */
  async showSyncStatus() {
    await this.logger.logUserAction('show-sync-status');

    const status = this.syncManager.getSyncStatus();
    const stats = this.syncManager.getSyncStats();

    await this.logger.info('Sync status requested', { status, stats });

    // TODO: Show status in UI modal
  }

  /**
   * Shows the data dashboard
   *
   * @returns {Promise<void>}
   */
  async showDataDashboard() {
    await this.logger.logUserAction('show-data-dashboard');

    if (!this.api.ui || !this.api.ui.showModal) {
      await this.logger.warn('Modal UI not available');
      return;
    }

    try {
      // Read the dashboard HTML file
      await this.logger.debug('About to call path.join for dashboard', {
        pathAvailable: typeof path,
        joinAvailable: typeof path.join,
        dirname: __dirname
      });

      const dashboardHtmlPath = path.join(__dirname, 'ui', 'dashboard.html');

      await this.logger.debug('path.join successful for dashboard', { dashboardHtmlPath });

      let dashboardHtml;
      try {
        dashboardHtml = fs.readFileSync(dashboardHtmlPath, 'utf8');
      } catch (error) {
        await this.logger.error('Failed to load dashboard HTML', error);
        // Fallback to inline HTML
        dashboardHtml = this.getInlineDashboardHTML();
      }

      // Show modal with dashboard content
      const modalId = this.api.ui.showModal({
        title: 'Limitless AI Dashboard',
        content: dashboardHtml,
        width: 1200,
        height: 800,
        buttons: [
          {
            text: 'Close',
            variant: 'secondary',
            callback: () => {
              this.api.ui.closeModal(modalId);
            }
          }
        ]
      });

      await this.logger.info('Dashboard opened', { modalId });

    } catch (error) {
      await this.logger.error('Failed to show dashboard', error);
    }
  }

  /**
   * Validates settings for IPC validation requests (updated to use APIKeyManager)
   *
   * @param {Object} settings - Settings object to validate
   * @param {Object} options - Validation options
   * @returns {Promise<Object>} Validation result {success: boolean, error?: string}
   */
  async validateSettings(settings, options = {}) {
    const validationId = `validation_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      await this.logger.debug('validateSettings called', {
        hasApiKey: !!settings?.apiKey,
        apiKeyLength: settings?.apiKey?.length || 0,
        apiKeyFirst4: settings?.apiKey?.substring(0, 4) || 'none',
        validationId,
        options
      });

      await this.logger.info('Plugin settings validation requested', {
        hasApiKey: !!settings?.apiKey,
        apiKeyLength: settings?.apiKey?.length || 0,
        validationId,
        options
      });

      // Enhanced validation logic - check for required API key
      if (!settings?.apiKey) {
        const error = 'API key is required for Limitless AI integration';
        await this.logger.debug('No API key provided');
        await this.logger.warn('Settings validation failed: missing API key', { validationId });
        return { success: false, error };
      }

      // Validate API key format
      if (typeof settings.apiKey !== 'string' || settings.apiKey.trim().length === 0) {
        const error = 'API key must be a non-empty string';
        await this.logger.debug('Invalid API key format');
        await this.logger.warn('Settings validation failed: invalid API key format', { validationId });
        return { success: false, error };
      }

      // For immediate storage mode, use direct storage without validation
      if (options.immediateStorage) {
        await this.logger.debug('Immediate storage mode - storing API key without network validation');
        await this.logger.info('Immediate storage mode - using direct storage', { validationId });

        // For immediate storage, we'll use the existing plugin storage mechanism
        // since APIKeyManager always validates before storing
        try {
          // Store in plugin storage for immediate access
          const currentSettings = this.api.storage.loadData() || {};
          currentSettings.apiKey = settings.apiKey.trim();
          const success = this.api.storage.saveData(currentSettings);

          if (success) {
            return {
              success: true,
              validated: false, // Indicates network validation is pending
              validationId,
              message: 'API key stored successfully, network validation pending'
            };
          } else {
            throw new Error('Failed to save to plugin storage');
          }
        } catch (storageError) {
          await this.logger.error('Failed to store API key', {
            error: storageError.message,
            validationId
          });
          return {
            success: false,
            error: `Failed to store API key: ${storageError.message}`,
            validationId
          };
        }
      }

      // For validateOnly mode (manual test connection), use APIKeyManager validation only
      if (options.validateOnly) {
        await this.logger.debug('Validate-only mode - performing network validation');
        await this.logger.info('Validate-only mode - using APIKeyManager for validation', { validationId });

        try {
          // Use APIKeyManager to validate the API key (without storing)
          const result = await this.apiKeyManager.validateAPIKey(settings.apiKey.trim(), {
            correlationId: validationId
          });

          await this.logger.info('Network validation completed', {
            success: result.success,
            error: result.error,
            validationId
          });

          return {
            success: result.success,
            error: result.error,
            validated: true,
            validationId
          };
        } catch (validationError) {
          await this.logger.error('API key validation failed', {
            error: validationError.message,
            validationId
          });
          return {
            success: false,
            error: `Validation failed: ${validationError.message}`,
            validated: true,
            validationId
          };
        }
      }

      // Default behavior - validate and store using APIKeyManager
      await this.logger.debug('Default validation mode - using APIKeyManager to validate and store');
      try {
        // Use APIKeyManager to validate and store the API key
        const result = await this.apiKeyManager.validateAndStoreAPIKey(settings.apiKey.trim(), {
          correlationId: validationId
        });

        return {
          success: result.success,
          validated: true,
          validationId,
          error: result.error,
          message: result.success ? 'API key validated and stored successfully' : 'API key validation failed'
        };
      } catch (error) {
        await this.logger.error('Failed to validate and store API key', {
          error: error.message,
          validationId
        });
        return {
          success: false,
          error: `Validation and storage failed: ${error.message}`,
          validationId
        };
      }

    } catch (error) {
      await this.logger.error('Settings validation error', {
        error: error.message,
        stack: error.stack,
        validationId
      });
      return {
        success: false,
        error: `Validation failed: ${error.message}`,
        validated: true,
        validationId
      };
    }
  }

  /**
   * Set up validation event listener
   *
   * @private
   */
  setupValidationEventListener() {
    EventBus.on('limitless:validation:complete', this.handleValidationComplete);
  }

  /**
   * Handle validation completion event
   *
   * @param {Object} eventData - Validation completion data
   * @private
   */
  handleValidationComplete(eventData) {
    const { pluginId, isValid, correlationId, error } = eventData;

    if (pluginId !== 'limitless') {
      return; // Not for this plugin
    }

    this.logger.info('Validation completion event received', {
      pluginId,
      isValid,
      correlationId,
      hasError: !!error
    });

    // Update internal state based on validation result
    if (isValid) {
      this.logger.info('API key validation successful - plugin fully enabled');
      // Plugin is now fully functional
    } else {
      this.logger.warn('API key validation failed - plugin remains disabled', {
        error: error?.message
      });
      // Plugin remains in limited functionality mode
    }
  }

  /**
   * Trigger background validation using APIKeyManager
   *
   * @param {string} correlationId - Optional correlation ID
   * @returns {Promise<void>}
   */
  async triggerBackgroundValidation(correlationId = null) {
    try {
      await this.logger.info('Triggering background validation', { correlationId });

      // Retrieve the stored API key from plugin storage
      const settings = this.api.storage.loadData();
      const apiKey = settings?.apiKey;

      if (!apiKey) {
        throw new Error('No API key found for background validation');
      }

      // Use APIKeyManager to validate and store the API key
      const validationResult = await this.apiKeyManager.validateAndStoreAPIKey(apiKey, {
        correlationId
      });

      // Emit validation completion event
      EventBus.emit('limitless:validation:complete', {
        pluginId: 'limitless',
        isValid: validationResult.success,
        correlationId,
        timestamp: new Date().toISOString(),
        error: validationResult.error ? {
          message: validationResult.error,
          code: 'VALIDATION_ERROR'
        } : null
      });

      await this.logger.info('Background validation completed', {
        correlationId,
        isValid: validationResult.success,
        error: validationResult.error
      });
    } catch (error) {
      await this.logger.error('Failed to trigger background validation', {
        error: error.message,
        correlationId
      });

      // Emit failure event
      EventBus.emit('limitless:validation:complete', {
        pluginId: 'limitless',
        isValid: false,
        correlationId,
        timestamp: new Date().toISOString(),
        error: {
          message: error.message,
          code: 'VALIDATION_ERROR'
        }
      });
    }
  }

  /**
   * Validates the stored API key using APIKeyManager
   *
   * @returns {Promise<void>}
   */
  async validateAPIKey() {
    try {
      await this.logger.logUserAction('validate-api-key');

      // Retrieve API key from plugin storage
      const settings = this.api.storage.loadData();
      const apiKey = settings?.apiKey;

      if (!apiKey) {
        await this.logger.warn('No API key to validate');
        return;
      }

      // Use APIKeyManager for validation
      const validationResult = await this.apiKeyManager.validateAPIKey(apiKey);

      if (validationResult.success) {
        await this.logger.info('API key validation successful');
      } else {
        await this.logger.warn('API key validation failed', {
          error: validationResult.error
        });
      }

    } catch (error) {
      await this.logger.error('API key validation error', error);
    }
  }

  /**
   * Clears all stored data
   *
   * @returns {Promise<void>}
   */
  async clearStoredData() {
    try {
      await this.logger.logUserAction('clear-stored-data');

      // Clear plugin storage
      const success = this.api.storage.saveData({});

      if (success) {
        await this.logger.info('All stored data cleared');
      } else {
        throw new Error('Failed to clear stored data');
      }

    } catch (error) {
      await this.logger.error('Failed to clear stored data', error);
    }
  }

  /**
   * Gets the current plugin status
   *
   * @returns {Object} Plugin status information
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      version: this.api.manifest.version,
      permissions: this.api.manifest.permissions,
      syncStatus: this.syncManager.getSyncStatus(),
      lastActivity: new Date().toISOString()
    };
  }

  /**
   * Cleanup when plugin is disabled/unloaded
   *
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      await this.logger.logLifecycle('plugin-cleanup-start');

      // Cleanup sync manager
      await this.syncManager.cleanup();

      // Remove UI elements
      if (this.ribbonIconId && this.api.ui && this.api.ui.removeRibbonIcon) {
        this.api.ui.removeRibbonIcon(this.ribbonIconId);
      }

      this.isInitialized = false;

      await this.logger.logLifecycle('plugin-cleanup-complete');

    } catch (error) {
      await this.logger.error('Failed during plugin cleanup', error);
    }
  }
}

// Export the LimitlessPlugin class for testing
module.exports.LimitlessPlugin = LimitlessPlugin;

// Initialize the plugin
try {
  // Check if PluginAPI is available (may not be in test environment)
  if (typeof PluginAPI !== 'undefined') {
    const plugin = new LimitlessPlugin(PluginAPI);

    // Initialize the plugin
    plugin.initialize().catch(async (error) => {
      console.error('Failed to initialize Limitless plugin:', error);
    });

    // Export the plugin for external access
    module.exports.plugin = plugin;
    module.exports.name = PluginAPI.manifest.name;
    module.exports.version = PluginAPI.manifest.version;
    module.exports.getStatus = () => plugin.getStatus();
    module.exports.cleanup = () => plugin.cleanup();
  }

} catch (error) {
  console.error('Error creating Limitless plugin:', error);
}
