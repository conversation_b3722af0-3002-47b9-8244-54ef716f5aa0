/**
 * Unit tests for the SyncManager class
 *
 * @module SyncManagerTests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const SyncManager = require('../src/sync-manager');

// Mock the global setTimeout and clearTimeout
const timers = require('timers');
const setTimeout = timers.setTimeout;
const clearTimeout = timers.clearTimeout;

// Mock the global Date
const RealDate = Date;
function mockDate(isoDate) {
  global.Date = class extends RealDate {
    constructor() {
      return new RealDate(isoDate);
    }
  };
  global.Date.now = () => new RealDate(isoDate).getTime();
}

describe('SyncManager', () => {
  let logger;
  let api;
  let weatherAPI;
  let dataProcessor;
  let syncManager;

  // Mock data
  const mockWeatherData = {
    current: {
      last_updated: '2023-06-01 12:00',
      temp_c: 25,
      condition: { code: 1000 }
    },
    forecast: {
      forecastday: [
        {
          date: '2023-06-01',
          day: {
            maxtemp_c: 28,
            condition: { code: 1000 }
          }
        }
      ]
    }
  };

  beforeEach(() => {
    // Reset all mocks
    sinon.resetHistory();

    // Reset the fake timers
    sinon.restore();

    // Set a fixed date for testing
    mockDate('2023-06-01T12:00:00Z');

    // Create a mock logger
    logger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    };

    // Create a mock API
    api = {
      settings: {
        get: sinon.stub().resolves({
          apiKey: 'test-api-key',
          latitude: 40.7128,
          longitude: -74.0060,
          units: 'metric',
          autoSync: true,
          syncInterval: 12 // hours
        })
      },
      storage: {
        get: sinon.stub().resolves(null),
        set: sinon.stub().resolves(),
        remove: sinon.stub().resolves()
      },
      notifications: {
        show: sinon.stub()
      },
      events: {
        emit: sinon.stub()
      },
      user: {
        id: 'test-user-123'
      }
    };

    // Create a mock WeatherAPI
    weatherAPI = {
      getWeatherData: sinon.stub().resolves(mockWeatherData),
      validateApiKey: sinon.stub().resolves(true)
    };

    // Create a mock DataProcessor
    dataProcessor = {
      processWeatherData: sinon.stub().resolves({
        current: { temp_c: 25 },
        forecast: [],
        lastUpdated: '2023-06-01T12:00:00Z'
      })
    };

    // Create a new instance of SyncManager for each test
    syncManager = new SyncManager(api, logger, weatherAPI, dataProcessor);
  });

  afterEach(() => {
    // Restore the original Date
    global.Date = RealDate;

    // Clear any pending timeouts
    sinon.clock && sinon.clock.restore();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      // Mock the storage to return no previous state
      api.storage.get.resolves(null);

      await syncManager.initialize();

      // Should load settings
      expect(api.settings.get.calledOnce).to.be.true;

      // Should start auto-sync
      expect(syncManager.syncInterval).to.not.be.null;
      expect(syncManager.nextSyncTime).to.not.be.null;

      // Should log initialization
      expect(logger.debug.calledWith('Sync manager initialized')).to.be.true;
    });

    it('should load previous sync state from storage', async () => {
      const lastSyncTime = '2023-06-01T10:00:00Z';
      const nextSyncTime = '2023-06-01T22:00:00Z';

      // Mock the storage to return previous state
      api.storage.get.resolves({
        lastSyncTime,
        nextSyncTime,
        syncStatus: 'idle',
        retryCount: 0
      });

      await syncManager.initialize();

      // Should load the previous state
      expect(syncManager.lastSyncTime).to.equal(lastSyncTime);
      expect(syncManager.nextSyncTime.toISOString()).to.equal(nextSyncTime);
      expect(syncManager.syncStatus).to.equal('idle');
    });

    it('should handle missing settings', async () => {
      // Make settings return an empty object
      api.settings.get.resolves({});

      await syncManager.initialize();

      // Should still initialize but with default values
      expect(syncManager.syncInterval).to.not.be.null;
      expect(syncManager.nextSyncTime).to.not.be.null;
    });
  });

  describe('Auto-Sync', () => {
    it('should start auto-sync with default interval', async () => {
      // Initialize with default settings
      await syncManager.initialize();

      // Should set up the interval
      expect(syncManager.syncInterval).to.not.be.null;

      // Should set next sync time to now + 12 hours (default interval)
      const expectedNextSync = new Date();
      expectedNextSync.setHours(expectedNextSync.getHours() + 12);

      // Allow for small time differences in test execution
      expect(syncManager.nextSyncTime.getTime() - expectedNextSync.getTime())
        .to.be.lessThan(1000);
    });

    it('should start auto-sync with custom interval', async () => {
      // Set a custom interval
      api.settings.get.resolves({
        autoSync: true,
        syncInterval: 24 // hours
      });

      await syncManager.initialize();

      // Should set next sync time to now + 24 hours
      const expectedNextSync = new Date();
      expectedNextSync.setHours(expectedNextSync.getHours() + 24);

      expect(syncManager.nextSyncTime.getTime() - expectedNextSync.getTime())
        .to.be.lessThan(1000);
    });

    it('should stop auto-sync', async () => {
      await syncManager.initialize();

      // Start with auto-sync enabled
      expect(syncManager.syncInterval).to.not.be.null;

      // Stop auto-sync
      await syncManager.stopAutoSync();

      // Should clear the interval
      expect(syncManager.syncInterval).to.be.null;
      expect(syncManager.nextSyncTime).to.be.null;

      // Should log the stop
      expect(logger.info.calledWith('Auto-sync stopped')).to.be.true;
    });

    it('should not start auto-sync if disabled in settings', async () => {
      // Disable auto-sync in settings
      api.settings.get.resolves({
        autoSync: false
      });

      await syncManager.initialize();

      // Should not set up the interval
      expect(syncManager.syncInterval).to.be.null;
      expect(syncManager.nextSyncTime).to.be.null;
    });
  });

  describe('Manual Sync', () => {
    it('should perform a manual sync successfully', async () => {
      // Initialize the sync manager
      await syncManager.initialize();

      // Perform a manual sync
      const result = await syncManager.performSync();

      // Should return success
      expect(result.success).to.be.true;
      expect(result.message).to.include('Sync completed successfully');

      // Should update sync state
      expect(syncManager.lastSyncTime).to.not.be.null;
      expect(syncManager.syncStatus).to.equal('idle');
      expect(syncManager.retryCount).to.equal(0);

      // Should fetch weather data
      expect(weatherAPI.getWeatherData.calledOnce).to.be.true;

      // Should process the weather data
      expect(dataProcessor.processWeatherData.calledOnce).to.be.true;

      // Should emit sync complete event
      expect(api.events.emit.calledWith('weather:sync-complete', {
        success: true,
        error: null,
        lastSync: syncManager.lastSyncTime,
        nextSync: syncManager.nextSyncTime
      })).to.be.true;

      // Should show success notification
      expect(api.notifications.show.calledWith({
        title: 'Weather Data Updated',
        message: 'Your weather data has been successfully updated',
        type: 'success',
        timeout: 5000
      })).to.be.true;
    });

    it('should handle sync in progress', async () => {
      // Make processWeatherData take a while
      dataProcessor.processWeatherData.callsFake(
        () => new Promise(resolve => setTimeout(() => resolve({}), 1000))
      );

      // Start a sync
      const syncPromise = syncManager.performSync();

      // Try to start another sync while the first is in progress
      const result = await syncManager.performSync();

      // Should return immediately with sync in progress message
      expect(result.success).to.be.false;
      expect(result.message).to.include('Sync already in progress');

      // Clean up
      await syncPromise;
    });

    it('should handle sync too soon after last sync', async () => {
      // Set a recent last sync time (5 minutes ago)
      syncManager.lastSyncTime = new Date(Date.now() - 5 * 60 * 1000).toISOString();

      // Try to sync again
      const result = await syncManager.performSync();

      // Should return without syncing
      expect(result.success).to.be.false;
      expect(result.message).to.include('Minimum sync interval not reached');

      // Should not have fetched any data
      expect(weatherAPI.getWeatherData.called).to.be.false;
    });

    it('should handle missing API key', async () => {
      // Make settings return no API key
      api.settings.get.resolves({
        latitude: 40.7128,
        longitude: -74.0060
      });

      // Try to sync
      const result = await syncManager.performSync();

      // Should return error
      expect(result.success).to.be.false;
      expect(result.error).to.include('Weather API key not configured');

      // Should log the error
      expect(logger.error.called).to.be.true;
    });

    it('should handle missing location', async () => {
      // Make settings return no location
      api.settings.get.resolves({
        apiKey: 'test-api-key'
      });

      // Try to sync
      const result = await syncManager.performSync();

      // Should return error
      expect(result.success).to.be.false;
      expect(result.error).to.include('Location not configured');
    });

    it('should handle API errors with retry', async () => {
      // Make the API call fail
      const error = new Error('API error');
      weatherAPI.getWeatherData.rejects(error);

      // Set up fake timers for testing retry
      const clock = sinon.useFakeTimers();

      // Start the sync
      const syncPromise = syncManager.performSync();

      // Advance time to trigger retries
      for (let i = 0; i < 3; i++) {
        // Wait for the current retry delay
        await clock.tickAsync(syncManager.retryDelay * (i + 1));
      }

      // The sync should eventually fail after all retries
      const result = await syncPromise;

      // Should have retried the maximum number of times
      expect(weatherAPI.getWeatherData.callCount).to.equal(4); // Initial + 3 retries

      // Should return error
      expect(result.success).to.be.false;
      expect(result.error).to.include('API error');
      expect(result.willRetry).to.be.false;

      // Should show error notification after all retries
      expect(api.notifications.show.calledWith({
        title: 'Weather Sync Failed',
        message: 'Failed to update weather data after 3 attempts. API error',
        type: 'error',
        timeout: 10000
      })).to.be.true;

      // Clean up
      clock.restore();
    });
  });

  describe('Sync State Management', () => {
    it('should save sync state to storage', async () => {
      // Initialize the sync manager
      await syncManager.initialize();

      // Perform a sync
      await syncManager.performSync();

      // Should have saved the sync state
      expect(api.storage.set.calledWith('syncState', {
        lastSyncTime: syncManager.lastSyncTime,
        nextSyncTime: syncManager.nextSyncTime.toISOString(),
        syncStatus: syncManager.syncStatus,
        syncError: syncManager.syncError,
        retryCount: syncManager.retryCount
      })).to.be.true;
    });

    it('should reset sync state', async () => {
      // Set some state
      syncManager.lastSyncTime = '2023-06-01T10:00:00Z';
      syncManager.nextSyncTime = new Date('2023-06-01T22:00:00Z');
      syncManager.syncStatus = 'syncing';
      syncManager.syncError = 'Test error';
      syncManager.retryCount = 3;

      // Reset the state
      syncManager._resetSyncState();

      // Should reset to initial state
      expect(syncManager.lastSyncTime).to.be.null;
      expect(syncManager.nextSyncTime).to.be.null;
      expect(syncManager.syncStatus).to.equal('idle');
      expect(syncManager.syncError).to.be.null;
      expect(syncManager.retryCount).to.equal(0);
    });
  });

  describe('Cleanup', () => {
    it('should clean up resources', async () => {
      // Initialize and start auto-sync
      await syncManager.initialize();

      // Set up a fake interval
      const intervalId = setInterval(() => {}, 1000);
      syncManager.syncInterval = intervalId;

      // Spy on clearInterval
      const clearIntervalSpy = sinon.spy(global, 'clearInterval');

      // Clean up
      await syncManager.cleanup();

      // Should clear the interval
      expect(clearIntervalSpy.calledWith(intervalId)).to.be.true;

      // Should save the current state
      expect(api.storage.set.calledOnce).to.be.true;

      // Clean up
      clearIntervalSpy.restore();
      clearInterval(intervalId);
    });
  });
});
