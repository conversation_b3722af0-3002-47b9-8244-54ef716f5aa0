/**
 * Unit tests for the UnitConverter class
 *
 * @module UnitConverterTests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const UnitConverter = require('../src/unit-converter');

describe('UnitConverter', () => {
  let logger;
  let converter;

  beforeEach(() => {
    // Create a mock logger
    logger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    };

    // Create a new instance of UnitConverter for each test
    converter = new UnitConverter(logger);
  });

  describe('Temperature Conversion', () => {
    it('should convert Celsius to Fahrenheit', () => {
      const result = converter.convertTemperature(25, 'c', 'f');
      expect(result).to.equal(77); // 25°C = 77°F
    });

    it('should convert Fahrenheit to Celsius', () => {
      const result = converter.convertTemperature(77, 'f', 'c');
      expect(result).to.equal(25); // 77°F = 25°C
    });

    it('should handle zero values correctly', () => {
      const cToF = converter.convertTemperature(0, 'c', 'f');
      expect(cToF).to.equal(32); // 0°C = 32°F

      const fToC = converter.convertTemperature(32, 'f', 'c');
      expect(fToC).to.equal(0); // 32°F = 0°C
    });

    it('should handle negative temperatures', () => {
      const cToF = converter.convertTemperature(-10, 'c', 'f');
      expect(cToF).to.equal(14); // -10°C = 14°F

      const fToC = converter.convertTemperature(14, 'f', 'c');
      expect(fToC).to.equal(-10); // 14°F = -10°C
    });

    it('should round to specified decimal places', () => {
      const result = converter.convertTemperature(37.7778, 'c', 'f', 1);
      expect(result).to.equal(100.0); // 37.7778°C = 100.0°F
    });
  });

  describe('Speed Conversion', () => {
    it('should convert km/h to mph', () => {
      const result = converter.convertSpeed(100, 'kmh', 'mph');
      expect(result).to.be.closeTo(62.1371, 0.001); // 100 km/h ≈ 62.1371 mph
    });

    it('should convert mph to km/h', () => {
      const result = converter.convertSpeed(60, 'mph', 'kmh');
      expect(result).to.be.closeTo(96.5604, 0.001); // 60 mph ≈ 96.5604 km/h
    });
  });

  describe('Distance Conversion', () => {
    it('should convert kilometers to miles', () => {
      const result = converter.convertDistance(10, 'km', 'mi');
      expect(result).to.be.closeTo(6.2137, 0.001); // 10 km ≈ 6.2137 mi
    });

    it('should convert miles to kilometers', () => {
      const result = converter.convertDistance(10, 'mi', 'km');
      expect(result).to.be.closeTo(16.0934, 0.001); // 10 mi ≈ 16.0934 km
    });
  });

  describe('Precipitation Conversion', () => {
    it('should convert millimeters to inches', () => {
      const result = converter.convertPrecipitation(25.4, 'mm', 'in');
      expect(result).to.be.closeTo(1.0, 0.001); // 25.4 mm = 1 in
    });

    it('should convert inches to millimeters', () => {
      const result = converter.convertPrecipitation(1, 'in', 'mm');
      expect(result).to.be.closeTo(25.4, 0.001); // 1 in = 25.4 mm
    });
  });

  describe('Pressure Conversion', () => {
    it('should convert millibars to inches of mercury', () => {
      const result = converter.convertPressure(1013.25, 'mb', 'inHg');
      expect(result).to.be.closeTo(29.9213, 0.001); // 1013.25 mb ≈ 29.9213 inHg
    });

    it('should convert inches of mercury to millibars', () => {
      const result = converter.convertPressure(29.92, 'inHg', 'mb');
      expect(result).to.be.closeTo(1013.25, 0.1); // 29.92 inHg ≈ 1013.25 mb
    });
  });

  describe('Weather Data Conversion', () => {
    const sampleWeatherData = {
      current: {
        temp_c: 25,
        temp_f: 77,
        feelslike_c: 26,
        feelslike_f: 78.8,
        wind_kph: 10,
        wind_mph: 6.2,
        pressure_mb: 1013,
        pressure_in: 29.92,
        precip_mm: 10,
        precip_in: 0.39,
        humidity: 60,
        condition: {
          code: 1000,
          text: 'Sunny'
        },
        uv: 7,
        vis_km: 10,
        vis_miles: 6.2
      },
      forecast: {
        forecastday: [
          {
            date: '2023-06-01',
            day: {
              maxtemp_c: 28,
              maxtemp_f: 82.4,
              mintemp_c: 18,
              mintemp_f: 64.4,
              avgtemp_c: 23,
              avgtemp_f: 73.4,
              maxwind_kph: 15,
              maxwind_mph: 9.3,
              totalprecip_mm: 0,
              totalprecip_in: 0,
              avgvis_km: 10,
              avgvis_miles: 6,
              avghumidity: 65,
              condition: {
                code: 1000,
                text: 'Sunny'
              },
              uv: 8
            },
            hour: [
              {
                time: '2023-06-01 12:00',
                temp_c: 27,
                temp_f: 80.6,
                is_day: 1,
                condition: {
                  code: 1000,
                  text: 'Sunny'
                },
                wind_kph: 12,
                wind_mph: 7.5,
                wind_degree: 180,
                wind_dir: 'S',
                pressure_mb: 1012,
                pressure_in: 29.88,
                precip_mm: 0,
                precip_in: 0,
                humidity: 55,
                feelslike_c: 28,
                feelslike_f: 82.4,
                windchill_c: 27,
                windchill_f: 80.6,
                heatindex_c: 29,
                heatindex_f: 84.2,
                dewpoint_c: 17,
                dewpoint_f: 62.6,
                will_it_rain: 0,
                chance_of_rain: 0,
                will_it_snow: 0,
                chance_of_snow: 0,
                vis_km: 10,
                vis_miles: 6,
                gust_kph: 15,
                gust_mph: 9.3,
                uv: 8
              }
            ]
          }
        ]
      }
    };

    it('should convert weather data from metric to imperial', () => {
      const converted = converter.convertWeatherData(sampleWeatherData, 'metric', 'imperial');

      // Check current weather
      expect(converted.current.temp_f).to.equal(77);
      expect(converted.current.feelslike_f).to.be.closeTo(78.8, 0.1);
      expect(converted.current.wind_mph).to.be.closeTo(6.2, 0.1);
      expect(converted.current.pressure_in).to.be.closeTo(29.92, 0.1);

      // Check forecast
      const forecastDay = converted.forecast.forecastday[0].day;
      expect(forecastDay.maxtemp_f).to.equal(82.4);
      expect(forecastDay.maxwind_mph).to.be.closeTo(9.3, 0.1);

      // Check hourly data
      const hour = converted.forecast.forecastday[0].hour[0];
      expect(hour.temp_f).to.equal(80.6);
      expect(hour.feelslike_f).to.equal(82.4);
    });

    it('should convert weather data from imperial to metric', () => {
      // Create an imperial version of the sample data
      const imperialData = JSON.parse(JSON.stringify(sampleWeatherData));
      imperialData.current = { ...sampleWeatherData.current };
      imperialData.forecast = { forecastday: sampleWeatherData.forecast.forecastday.map(day => ({
        ...day,
        day: { ...day.day }
      }))};

      const converted = converter.convertWeatherData(imperialData, 'imperial', 'metric');

      // Check current weather
      expect(converted.current.temp_c).to.equal(25);
      expect(converted.current.feelslike_c).to.equal(26);
      expect(converted.current.wind_kph).to.equal(10);
      expect(converted.current.pressure_mb).to.equal(1013);
    });
  });

  describe('Unit Symbols', () => {
    it('should return correct unit symbols for metric system', () => {
      expect(converter.getUnitSymbol('temperature', 'metric')).to.equal('°C');
      expect(converter.getUnitSymbol('speed', 'metric')).to.equal('km/h');
      expect(converter.getUnitSymbol('distance', 'metric')).to.equal('km');
      expect(converter.getUnitSymbol('precipitation', 'metric')).to.equal('mm');
      expect(converter.getUnitSymbol('pressure', 'metric')).to.equal('mb');
      expect(converter.getUnitSymbol('visibility', 'metric')).to.equal('km');
    });

    it('should return correct unit symbols for imperial system', () => {
      expect(converter.getUnitSymbol('temperature', 'imperial')).to.equal('°F');
      expect(converter.getUnitSymbol('speed', 'imperial')).to.equal('mph');
      expect(converter.getUnitSymbol('distance', 'imperial')).to.equal('mi');
      expect(converter.getUnitSymbol('precipitation', 'imperial')).to.equal('in');
      expect(converter.getUnitSymbol('pressure', 'imperial')).to.equal('inHg');
      expect(converter.getUnitSymbol('visibility', 'imperial')).to.equal('mi');
    });
  });

  describe('Error Handling', () => {
    it('should return original value on unsupported conversion', () => {
      const originalValue = 100;
      const result = converter.convertSpeed(originalValue, 'invalid', 'invalid');
      expect(result).to.equal(originalValue);

      // Verify error was logged
      expect(logger.error.calledOnce).to.be.true;
    });

    it('should handle null or undefined values', () => {
      expect(converter.convertTemperature(null, 'c', 'f')).to.be.null;
      expect(converter.convertTemperature(undefined, 'c', 'f')).to.be.undefined;

      // Should not throw errors for null/undefined values
      expect(() => {
        converter.convertTemperature(null, 'c', 'f');
        converter.convertTemperature(undefined, 'c', 'f');
      }).to.not.throw();
    });
  });
});
