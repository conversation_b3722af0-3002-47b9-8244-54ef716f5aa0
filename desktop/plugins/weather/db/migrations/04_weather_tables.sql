-- Migration: Create weather_forecasts table
-- This migration creates the weather_forecasts table to store weather data for the weather plugin

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the weather_forecasts table
CREATE TABLE IF NOT EXISTS weather_forecasts (
  -- Primary key
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

  -- User reference (foreign key to auth.users)
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Location information
  latitude DECIMAL(10, 6) NOT NULL,
  longitude DECIMAL(10, 6) NOT NULL,

  -- Date and time information
  forecast_date DATE NOT NULL,
  forecast_start TIMESTAMPTZ NOT NULL,
  forecast_end TIMESTAMPTZ NOT NULL,
  is_daytime BOOLEAN NOT NULL,

  -- Weather condition
  condition_code VARCHAR(50) NOT NULL,

  -- Temperature and feels like
  temperature DECIMAL(5, 2) NOT NULL,
  feels_like DECIMAL(5, 2) NOT NULL,

  -- Humidity and precipitation
  humidity DECIMAL(5, 2) NOT NULL,
  precipitation_chance DECIMAL(5, 2) NOT NULL,
  precipitation_type VARCHAR(50),
  precipitation_amount DECIMAL(8, 2) NOT NULL DEFAULT 0,
  snowfall_amount DECIMAL(8, 2) NOT NULL DEFAULT 0,

  -- Wind information
  wind_speed DECIMAL(8, 2) NOT NULL,
  wind_direction INTEGER NOT NULL,
  wind_direction_code VARCHAR(3) NOT NULL,

  -- Atmospheric conditions
  pressure DECIMAL(8, 2) NOT NULL,
  visibility DECIMAL(8, 2) NOT NULL,
  uv_index DECIMAL(5, 2) NOT NULL,

  -- Air quality data (stored as JSONB for flexibility)
  air_quality JSONB NOT NULL DEFAULT '{}'::jsonb,

  -- Units used for the stored values
  units VARCHAR(10) NOT NULL DEFAULT 'metric',

  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_weather_forecasts_user_date ON weather_forecasts(user_id, forecast_date, is_daytime);
CREATE INDEX IF NOT EXISTS idx_weather_forecasts_location_date ON weather_forecasts(latitude, longitude, forecast_date);
CREATE INDEX IF NOT EXISTS idx_weather_forecasts_created_at ON weather_forecasts(created_at);

-- Add RLS (Row Level Security) policies if using Supabase Auth
ALTER TABLE weather_forecasts ENABLE ROW LEVEL SECURITY;

-- Users can only access their own weather data
CREATE POLICY "Users can view their own weather data"
ON weather_forecasts
FOR SELECT
USING (auth.uid() = user_id);

-- Users can insert their own weather data
CREATE POLICY "Users can insert their own weather data"
ON weather_forecasts
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Users can update their own weather data
CREATE POLICY "Users can update their own weather data"
ON weather_forecasts
FOR UPDATE
USING (auth.uid() = user_id);

-- Users can delete their own weather data
CREATE POLICY "Users can delete their own weather data"
ON weather_forecasts
FOR DELETE
USING (auth.uid() = user_id);

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_weather_forecasts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_weather_forecasts_updated_at
BEFORE UPDATE ON weather_forecasts
FOR EACH ROW
EXECUTE FUNCTION update_weather_forecasts_updated_at();

-- Create a function to get weather for a specific date range
CREATE OR REPLACE FUNCTION get_weather_forecast(
  p_user_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_include_daytime BOOLEAN DEFAULT TRUE,
  p_include_nighttime BOOLEAN DEFAULT TRUE
)
RETURNS TABLE (
  id UUID,
  forecast_date DATE,
  is_daytime BOOLEAN,
  condition_code VARCHAR(50),
  temperature DECIMAL(5, 2),
  feels_like DECIMAL(5, 2),
  humidity DECIMAL(5, 2),
  precipitation_chance DECIMAL(5, 2),
  precipitation_type VARCHAR(50),
  wind_speed DECIMAL(8, 2),
  wind_direction_code VARCHAR(3),
  uv_index DECIMAL(5, 2),
  units VARCHAR(10)
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    wf.id,
    wf.forecast_date,
    wf.is_daytime,
    wf.condition_code,
    wf.temperature,
    wf.feels_like,
    wf.humidity,
    wf.precipitation_chance,
    wf.precipitation_type,
    wf.wind_speed,
    wf.wind_direction_code,
    wf.uv_index,
    wf.units
  FROM
    weather_forecasts wf
  WHERE
    wf.user_id = p_user_id
    AND wf.forecast_date BETWEEN p_start_date AND p_end_date
    AND (
      (p_include_daytime = TRUE AND wf.is_daytime = TRUE) OR
      (p_include_nighttime = TRUE AND wf.is_daytime = FALSE)
    )
  ORDER BY
    wf.forecast_date,
    wf.is_daytime;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create a function to get the latest weather data for a location
CREATE OR REPLACE FUNCTION get_latest_weather(
  p_user_id UUID,
  p_latitude DECIMAL(10, 6),
  p_longitude DECIMAL(10, 6),
  p_days_ahead INTEGER DEFAULT 5
)
RETURNS TABLE (
  forecast_date DATE,
  is_daytime BOOLEAN,
  condition_code VARCHAR(50),
  temperature DECIMAL(5, 2),
  feels_like DECIMAL(5, 2),
  humidity DECIMAL(5, 2),
  precipitation_chance DECIMAL(5, 2),
  wind_speed DECIMAL(8, 2),
  wind_direction_code VARCHAR(3),
  uv_index DECIMAL(5, 2)
) AS $$
BEGIN
  RETURN QUERY
  WITH latest_forecasts AS (
    SELECT
      wf.forecast_date,
      wf.is_daytime,
      wf.condition_code,
      wf.temperature,
      wf.feels_like,
      wf.humidity,
      wf.precipitation_chance,
      wf.wind_speed,
      wf.wind_direction_code,
      wf.uv_index,
      ROW_NUMBER() OVER (
        PARTITION BY wf.forecast_date, wf.is_daytime
        ORDER BY wf.updated_at DESC
      ) as rn
    FROM
      weather_forecasts wf
    WHERE
      wf.user_id = p_user_id
      AND wf.latitude = p_latitude
      AND wf.longitude = p_longitude
      AND wf.forecast_date BETWEEN CURRENT_DATE AND (CURRENT_DATE + (p_days_ahead || ' days')::interval)
  )
  SELECT
    forecast_date,
    is_daytime,
    condition_code,
    temperature,
    feels_like,
    humidity,
    precipitation_chance,
    wind_speed,
    wind_direction_code,
    uv_index
  FROM
    latest_forecasts
  WHERE
    rn = 1
  ORDER BY
    forecast_date,
    is_daytime DESC;
END;
$$ LANGUAGE plpgsql STABLE;
