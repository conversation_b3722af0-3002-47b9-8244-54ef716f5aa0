/**
 * Weather Plugin
 *
 * Main entry point for the Weather plugin.
 * Provides weather data to other Lifeboard components through a clean API.
 *
 * Features:
 * - Secure API key management for weather data providers
 * - 12-hour scheduled weather data synchronization
 * - Support for both Metric and Imperial units
 * - Location-based weather forecasting
 * - Detailed 5-day forecast data
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Import plugin modules
const WeatherAPI = require('./src/weather-api');
const DataProcessor = require('./src/data-processor');
const SyncManager = require('./src/sync-manager');

/**
 * Main Weather Plugin Class
 *
 * Provides weather data to other Lifeboard components through a clean API.
 *
 * @class WeatherPlugin
 */
class WeatherPlugin {
  /**
   * Creates a new WeatherPlugin instance
   *
   * @param {Object} api - The Plugin API instance
   */
  constructor(api) {
    this.api = api;
    this.logger = api.logger;
    this.weatherAPI = new WeatherAPI(api, this.logger);
    this.dataProcessor = new DataProcessor(api, this.logger);
    this.syncManager = new SyncManager(api, this.logger, this.weatherAPI, this.dataProcessor);
    this.isInitialized = false;
  }

  /**
   * Initializes the plugin
   *
   * Sets up data synchronization and registers API endpoints
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.logger.logLifecycle('weather-plugin-init-start');

      // Initialize sync manager
      await this.syncManager.initialize();

      // Register API endpoints
      await this.registerEndpoints();

      this.isInitialized = true;

      await this.logger.logLifecycle('weather-plugin-init-complete', {
        sync: 'initialized',
        endpoints: 'registered'
      });

      await this.logger.info('Weather plugin initialized successfully');
    } catch (error) {
      await this.logger.error('Failed to initialize Weather plugin', error);
      throw error;
    }
  }

  /**
   * Registers API endpoints for other components to access weather data
   *
   * @returns {Promise<void>}
   */
  async registerEndpoints() {
    try {
      // Get current weather data
      this.api.registerEndpoint('weather/current', async (params) => {
        try {
          return await this.dataProcessor.getCurrentWeather();
        } catch (error) {
          this.logger.error('Error fetching current weather', error);
          throw new Error('Failed to fetch current weather data');
        }
      });

      // Get forecast data
      this.api.registerEndpoint('weather/forecast', async (params) => {
        try {
          const days = params?.days || 5;
          return await this.dataProcessor.getForecast(days);
        } catch (error) {
          this.logger.error('Error fetching forecast', error);
          throw new Error('Failed to fetch forecast data');
        }
      });

      // Get weather alerts
      this.api.registerEndpoint('weather/alerts', async () => {
        try {
          return await this.dataProcessor.getWeatherAlerts();
        } catch (error) {
          this.logger.error('Error fetching weather alerts', error);
          throw new Error('Failed to fetch weather alerts');
        }
      });

      // Manually trigger a sync
      this.api.registerEndpoint('weather/sync', async (params, context) => {
        if (!context.user) {
          throw new Error('Authentication required');
        }

        try {
          const result = await this.syncManager.performSync();
          return { success: true, message: 'Weather data synced successfully', ...result };
        } catch (error) {
          this.logger.error('Error during manual sync', error);
          throw new Error('Failed to sync weather data');
        }
      });

      // Get plugin status
      this.api.registerEndpoint('weather/status', async () => {
        try {
          return {
            isInitialized: this.isInitialized,
            lastSync: this.syncManager.lastSyncTime,
            nextSync: this.syncManager.nextSyncTime,
            syncStatus: this.syncManager.syncStatus,
            hasApiKey: !!(await this.api.settings.get('apiKey')),
            hasLocation: !!(await this.api.settings.get('latitude') && await this.api.settings.get('longitude'))
          };
        } catch (error) {
          this.logger.error('Error getting plugin status', error);
          throw new Error('Failed to get plugin status');
        }
      });

      await this.logger.info('Weather plugin API endpoints registered successfully');
    } catch (error) {
      await this.logger.error('Failed to register API endpoints', error);
      throw error;
    }
  }

  /**
   * Checks initial setup and starts sync if configured
   *
   * @returns {Promise<void>}
   */
  async checkInitialSetup() {
    try {
      const settings = await this.api.settings.get();

      if (settings && settings.apiKey && settings.latitude && settings.longitude) {
        // If we have all required settings, start sync
        await this.syncManager.startAutoSync();
      } else {
        await this.logger.info('Weather plugin not fully configured. Please set up API key and location.');
      }
    } catch (error) {
      await this.logger.error('Error during initial setup check', error);
    }
  }

  /**
   * Shows the configuration modal
   *
   * @returns {Promise<void>}
   */
  async showConfigurationModal() {
    try {
      if (this.api.windows && this.api.windows.open) {
        // Use the new window system if available
        await this.api.windows.open({
          id: 'weather-settings',
          title: 'Weather Plugin Settings',
          width: 800,
          height: 600,
          html: await this.getSettingsHTML(),
          css: await this.getSettingsCSS(),
          js: await this.getSettingsJS()
        });
      } else {
        // Fallback to modal
        await this.api.modal.open({
          title: 'Weather Plugin Settings',
          content: this.getInlineSettingsHTML(),
          buttons: [
            {
              text: 'Cancel',
              onClick: () => this.api.modal.close()
            },
            {
              text: 'Save',
              type: 'primary',
              onClick: async () => {
                await this.saveSettings();
                this.api.modal.close();
              }
            }
          ]
        });
      }
    } catch (error) {
      await this.logger.error('Failed to show configuration modal', error);
    }
  }

  /**
   * Performs a manual sync of weather data
   *
   * @returns {Promise<void>}
   */
  async performManualSync() {
    try {
      await this.syncManager.performSync();
    } catch (error) {
      await this.logger.error('Manual sync failed', error);
      throw error;
    }
  }

  /**
   * Shows the weather dashboard
   *
   * @returns {Promise<void>}
   */
  async showWeatherDashboard() {
    try {
      if (this.api.windows && this.api.windows.open) {
        await this.api.windows.open({
          id: 'weather-dashboard',
          title: 'Weather Dashboard',
          width: 1000,
          height: 800,
          html: await this.getDashboardHTML(),
          css: await this.getDashboardCSS(),
          js: await this.getDashboardJS()
        });
      } else {
        // Fallback to modal
        await this.api.modal.open({
          title: 'Weather Dashboard',
          content: this.getInlineDashboardHTML(),
          width: '90%',
          height: '90%'
        });
      }
    } catch (error) {
      await this.logger.error('Failed to show weather dashboard', error);
    }
  }

  /**
   * Shows the raw weather data view
   *
   * @returns {Promise<void>}
   */
  async showWeatherDataView() {
    try {
      const weatherData = await this.dataProcessor.getStoredWeatherData();

      let content = '<div class="weather-data-view">';
      content += '<h3>Stored Weather Data</h3>';

      if (weatherData && weatherData.length > 0) {
        content += '<pre>' + JSON.stringify(weatherData, null, 2) + '</pre>';
      } else {
        content += '<p>No weather data available. Try syncing first.</p>';
      }

      content += '</div>';

      await this.api.modal.open({
        title: 'Weather Data',
        content: content,
        width: '80%',
        height: '80%'
      });
    } catch (error) {
      await this.logger.error('Failed to show weather data view', error);
    }
  }

  /**
   * Validates the API key with the weather service
   *
   * @returns {Promise<boolean>} Whether the API key is valid
   */
  async validateAPIKey() {
    try {
      const isValid = await this.weatherAPI.validateAPIKey();

      if (isValid) {
        await this.api.notifications.success('API Key is valid');
      } else {
        await this.api.notifications.error('API Key validation failed');
      }

      return isValid;
    } catch (error) {
      await this.logger.error('API Key validation error', error);
      await this.api.notifications.error('Error validating API Key: ' + error.message);
      return false;
    }
  }

  /**
   * Gets the current plugin status
   *
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      lastSync: this.syncManager ? this.syncManager.getLastSyncTime() : null,
      nextSync: this.syncManager ? this.syncManager.getNextSyncTime() : null,
      syncStatus: this.syncManager ? this.syncManager.getSyncStatus() : 'unknown'
    };
  }

  /**
   * Cleans up resources when the plugin is disabled
   *
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      await this.logger.logLifecycle('weather-plugin-cleanup-start');

      // Cleanup sync manager
      if (this.syncManager) {
        await this.syncManager.cleanup();
      }

      // Remove UI elements
      if (this.ribbonIconId && this.api.ui && this.api.ui.removeRibbonIcon) {
        this.api.ui.removeRibbonIcon(this.ribbonIconId);
      }

      this.isInitialized = false;

      await this.logger.logLifecycle('weather-plugin-cleanup-complete');
    } catch (error) {
      await this.logger.error('Failed during plugin cleanup', error);
    }
  }

  // Helper methods for UI content will be implemented in the next step
  async getSettingsHTML() { return ''; }
  async getSettingsCSS() { return ''; }
  async getSettingsJS() { return ''; }
  async getDashboardHTML() { return ''; }
  async getDashboardCSS() { return ''; }
  async getDashboardJS() { return ''; }
  getInlineSettingsHTML() { return ''; }
  getInlineDashboardHTML() { return ''; }
}

// Export the WeatherPlugin class for testing
module.exports.WeatherPlugin = WeatherPlugin;

// Initialize the plugin
try {
  // Check if PluginAPI is available (may not be in test environment)
  if (typeof PluginAPI !== 'undefined') {
    const plugin = new WeatherPlugin(PluginAPI);

    // Initialize the plugin
    plugin.initialize().catch(async (error) => {
      console.error('Failed to initialize Weather plugin:', error);
      if (plugin.logger) {
        await plugin.logger.error('Failed to initialize Weather plugin', error);
      }
    });

    // Export the plugin for external access
    module.exports.plugin = plugin;
    module.exports.name = 'Weather Plugin';
    module.exports.version = '1.0.0';
    module.exports.getStatus = () => plugin.getStatus();
    module.exports.cleanup = () => plugin.cleanup();
  }
} catch (error) {
  console.error('Error creating Weather plugin:', error);
}
