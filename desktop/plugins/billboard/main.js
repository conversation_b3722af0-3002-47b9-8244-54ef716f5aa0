/**
 * Billboard Data Collector for Lifeboard
 * Fetches and stores Billboard Hot 100 chart data in Supabase
 */

const fetch = require('node-fetch');

class BillboardPlugin {
  constructor(api) {
    this.api = api;
    this.settings = {
      rapidApiKey: null,
      lastFetched: null,
      refreshInterval: 8 * 60 * 60 * 1000, // 8 hours in milliseconds
    };
    this.intervalId = null;
  }

  /**
   * Initialize the plugin
   */
  async initialize() {
    try {
      await this.loadSettings();
      await this.ensureTableExists();

      if (this.settings.rapidApiKey) {
        await this.fetchAndStoreChartData();
        this.startRefreshInterval();
      }

      this.api.logger.info('Billboard Data Collector initialized');
    } catch (error) {
      this.api.logger.error('Error initializing Billboard Data Collector:', error);
    }
  }

  /**
   * Ensure the billboard_charts table exists
   */
  async ensureTableExists() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS billboard_charts (
        id SERIAL PRIMARY KEY,
        chart_date DATE NOT NULL,
        rank INTEGER NOT NULL,
        title TEXT NOT NULL,
        artist TEXT NOT NULL,
        weeks_at_no1 INTEGER DEFAULT 0,
        last_week INTEGER,
        peak_position INTEGER NOT NULL,
        weeks_on_chart INTEGER NOT NULL,
        detail TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(chart_date, rank)
      );
    `;

    await this.api.database.query(createTableSQL);
  }

  /**
   * Load plugin settings from storage
   */
  async loadSettings() {
    try {
      const savedSettings = await this.api.storage.get('billboard-settings');
      if (savedSettings) {
        this.settings = { ...this.settings, ...savedSettings };
      }
    } catch (error) {
      this.api.logger.error('Error loading settings:', error);
    }
  }

  /**
   * Save plugin settings to storage
   */
  async saveSettings(settings) {
    try {
      this.settings = { ...this.settings, ...settings };
      await this.api.storage.set('billboard-settings', this.settings);
    } catch (error) {
      this.api.logger.error('Error saving settings:', error);
      throw error;
    }
  }

  /**
   * Set the RapidAPI key
   */
  async setApiKey(apiKey) {
    if (!apiKey?.trim()) {
      throw new Error('API key is required');
    }

    // Test the API key
    const testUrl = 'https://billboard-api2.p.rapidapi.com/hot-100?date=2023-01-01&range=1-1';
    const response = await fetch(testUrl, {
      headers: {
        'x-rapidapi-key': apiKey.trim(),
        'x-rapidapi-host': 'billboard-api2.p.rapidapi.com',
      },
    });

    if (!response.ok) {
      throw new Error('Invalid API key or API error');
    }

    await this.saveSettings({ rapidApiKey: apiKey.trim() });
    await this.fetchAndStoreChartData();
    this.startRefreshInterval();
  }

  /**
   * Fetch chart data from Billboard API and store in Supabase
   */
  async fetchAndStoreChartData() {
    if (!this.settings.rapidApiKey) {
      throw new Error('RapidAPI key is not set');
    }

    try {
      const today = new Date().toISOString().split('T')[0];
      const url = `https://billboard-api2.p.rapidapi.com/hot-100?date=${today}&range=1-10`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'x-rapidapi-key': this.settings.rapidApiKey,
          'x-rapidapi-host': 'billboard-api2.p.rapidapi.com',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      await this.storeChartData(today, data);

      this.settings.lastFetched = new Date().toISOString();
      await this.saveSettings({ lastFetched: this.settings.lastFetched });

      this.api.logger.info('Successfully fetched and stored Billboard chart data');
      return true;
    } catch (error) {
      this.api.logger.error('Error fetching chart data:', error);
      throw error;
    }
  }

  /**
   * Store chart data in Supabase
   */
  async storeChartData(chartDate, data) {
    if (!data?.content) {
      throw new Error('Invalid chart data received');
    }

    const chartData = Object.values(data.content).map(item => ({
      chart_date: chartDate,
      rank: parseInt(item.rank, 10),
      title: item.title,
      artist: item.artist,
      weeks_at_no1: parseInt(item['weeks at no.1'] || '0', 10),
      last_week: parseInt(item['last week'] || '0', 10),
      peak_position: parseInt(item['peak position'], 10),
      weeks_on_chart: parseInt(item['weeks on chart'], 10),
      detail: item.detail,
    }));

    // Insert or update records in the database
    for (const item of chartData) {
      const { data: existing, error: selectError } = await this.api.database
        .from('billboard_charts')
        .select('id')
        .eq('chart_date', item.chart_date)
        .eq('rank', item.rank)
        .single();

      if (selectError && selectError.code !== 'PGRST116') { // PGRST116 = not found
        throw selectError;
      }

      if (existing) {
        // Update existing record
        const { error: updateError } = await this.api.database
          .from('billboard_charts')
          .update(item)
          .eq('id', existing.id);

        if (updateError) throw updateError;
      } else {
        // Insert new record
        const { error: insertError } = await this.api.database
          .from('billboard_charts')
          .insert([item]);

        if (insertError) throw insertError;
      }
    }
  }

  /**
   * Start the auto-refresh interval
   */
  startRefreshInterval() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(
      () => this.fetchAndStoreChartData(),
      this.settings.refreshInterval
    );
  }

  /**
   * Clean up resources when the plugin is unloaded
   */
  onunload() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}

// Initialize the plugin
const initPlugin = async (api) => {
  const plugin = new BillboardPlugin(api);

  // Expose API key setting method
  plugin.setApiKey = plugin.setApiKey.bind(plugin);

  await plugin.initialize();
  return plugin;
};

// Register the plugin
module.exports = initPlugin;
