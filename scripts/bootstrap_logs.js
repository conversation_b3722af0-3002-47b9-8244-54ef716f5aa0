#!/usr/bin/env node

/**
 * Bootstrap Log Directories Script
 * Creates all required log directories with proper permissions
 * Called by deployment and development startup scripts
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const PROJECT_ROOT = path.resolve(__dirname, '..');
const LOG_DIR = path.join(PROJECT_ROOT, 'logs');

// Required subdirectories
const REQUIRED_SUBDIRS = [
    'postgres',
    'auth',
    'realtime',
    'rest',
    'storage',
    'studio',
    'webui',
    'electron',
    'plugins'
];

// Colors for output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

/**
 * Structured logging function
 */
function log(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        level,
        component: 'bootstrap_logs',
        message,
        meta
    };

    // Console output with colors
    const color = colors[level] || colors.reset;
    console.log(`${color}[${timestamp}] ${level.toUpperCase()}: ${message}${colors.reset}`);

    // Write structured log if log directory exists
    if (fs.existsSync(LOG_DIR)) {
        try {
            const logFile = path.join(LOG_DIR, 'bootstrap.log');
            fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
        } catch (error) {
            // Ignore logging errors during bootstrap
        }
    }
}

/**
 * Check if directory exists and is writable
 */
function checkDirectoryWritable(dirPath) {
    try {
        // Check if directory exists
        if (!fs.existsSync(dirPath)) {
            return false;
        }

        // Test write permissions
        const testFile = path.join(dirPath, '.write-test');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Create directory with proper permissions
 */
function createDirectory(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true, mode: 0o700 });
            log('info', `Created directory: ${dirPath}`);
        } else {
            log('info', `Directory already exists: ${dirPath}`);
        }

        // Explicitly set permissions to 700 (owner: rwx, group: ---, others: ---)
        fs.chmodSync(dirPath, 0o700);
        log('info', `Set permissions 700 for: ${dirPath}`);

        // Verify permissions
        if (!checkDirectoryWritable(dirPath)) {
            throw new Error(`Directory ${dirPath} is not writable`);
        }

        return true;
    } catch (error) {
        log('red', `Failed to create directory ${dirPath}: ${error.message}`, { error: error.message });
        return false;
    }
}

/**
 * Validate all directories were created successfully
 */
function validateDirectories() {
    const errors = [];

    // Check main log directory
    if (!checkDirectoryWritable(LOG_DIR)) {
        errors.push(`Main log directory ${LOG_DIR} is not writable`);
    }

    // Check all subdirectories
    REQUIRED_SUBDIRS.forEach(subdir => {
        const subdirPath = path.join(LOG_DIR, subdir);
        if (!checkDirectoryWritable(subdirPath)) {
            errors.push(`Log subdirectory ${subdirPath} is not writable`);
        }
    });

    return errors;
}

/**
 * Get system information for logging
 */
function getSystemInfo() {
    try {
        return {
            platform: process.platform,
            nodeVersion: process.version,
            pid: process.pid,
            cwd: process.cwd(),
            user: process.env.USER || process.env.USERNAME || 'unknown'
        };
    } catch (error) {
        return { error: error.message };
    }
}

/**
 * Main bootstrap function
 */
function main() {
    const startTime = Date.now();

    log('blue', 'Starting log directory bootstrap process', {
        projectRoot: PROJECT_ROOT,
        logDir: LOG_DIR,
        requiredSubdirs: REQUIRED_SUBDIRS,
        systemInfo: getSystemInfo()
    });

    let success = true;

    // Create main log directory first
    if (!createDirectory(LOG_DIR)) {
        log('red', 'Failed to create main log directory, aborting');
        process.exit(1);
    }

    // Create all required subdirectories
    REQUIRED_SUBDIRS.forEach(subdir => {
        const subdirPath = path.join(LOG_DIR, subdir);
        if (!createDirectory(subdirPath)) {
            success = false;
        }
    });

    // Validate all directories
    const validationErrors = validateDirectories();
    if (validationErrors.length > 0) {
        log('red', 'Directory validation failed', { errors: validationErrors });
        validationErrors.forEach(error => {
            log('red', `Validation error: ${error}`);
        });
        success = false;
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (success) {
        log('green', 'Log directory bootstrap completed successfully', {
            duration: `${duration}ms`,
            directoriesCreated: REQUIRED_SUBDIRS.length + 1,
            logDir: LOG_DIR
        });

        // Summary output
        console.log(`\n${colors.green}✓ Bootstrap Summary:${colors.reset}`);
        console.log(`  Main directory: ${LOG_DIR}`);
        console.log(`  Subdirectories: ${REQUIRED_SUBDIRS.length}`);
        console.log(`  Total time: ${duration}ms`);
        console.log(`  Status: ${colors.green}SUCCESS${colors.reset}\n`);

        process.exit(0);
    } else {
        log('red', 'Log directory bootstrap failed', {
            duration: `${duration}ms`,
            validationErrors: validationErrors.length
        });

        console.log(`\n${colors.red}✗ Bootstrap failed!${colors.reset}`);
        console.log(`  Errors: ${validationErrors.length}`);
        console.log(`  Check permissions and disk space`);
        console.log(`  Log directory: ${LOG_DIR}\n`);

        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', () => {
    log('yellow', 'Bootstrap process interrupted');
    process.exit(130);
});

process.on('SIGTERM', () => {
    log('yellow', 'Bootstrap process terminated');
    process.exit(143);
});

// Error handling
process.on('uncaughtException', (error) => {
    log('red', 'Uncaught exception during bootstrap', { error: error.message, stack: error.stack });
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    log('red', 'Unhandled promise rejection during bootstrap', { reason: reason?.toString() });
    process.exit(1);
});

// Run main function
if (require.main === module) {
    main();
}

module.exports = {
    main,
    createDirectory,
    validateDirectories,
    checkDirectoryWritable,
    REQUIRED_SUBDIRS,
    LOG_DIR
};
