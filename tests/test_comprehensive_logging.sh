#!/bin/bash

#
# Comprehensive Logging System Test Script
# Tests all aspects of the implemented logging infrastructure
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Log test results
log_test() {
    local test_name="$1"
    local result="$2"
    local message="$3"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC}: $test_name"
        [ -n "$message" ] && echo "  $message"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC}: $test_name"
        [ -n "$message" ] && echo "  $message"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Test log directory structure
test_log_directories() {
    echo -e "${BLUE}Testing log directory structure...${NC}"

    # Required directories
    local required_dirs=(
        "logs"
        "logs/postgres"
        "logs/auth"
        "logs/realtime"
        "logs/rest"
        "logs/storage"
        "logs/studio"
        "logs/webui"
        "logs/electron"
        "logs/plugins"
    )

    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            # Check permissions (should be 700)
            local perms=$(stat -c "%a" "$dir" 2>/dev/null || stat -f "%A" "$dir" 2>/dev/null || echo "unknown")
            if [ "$perms" = "700" ]; then
                log_test "Directory $dir exists with correct permissions" "PASS" "Permissions: $perms"
            else
                log_test "Directory $dir has correct permissions" "FAIL" "Expected: 700, Got: $perms"
            fi
        else
            log_test "Directory $dir exists" "FAIL" "Directory not found"
        fi
    done
}

# Test bootstrap script
test_bootstrap_script() {
    echo -e "${BLUE}Testing bootstrap script...${NC}"

    if [ -f "scripts/bootstrap_logs.js" ]; then
        log_test "Bootstrap script exists" "PASS"

        # Test script execution
        if node scripts/bootstrap_logs.js >/dev/null 2>&1; then
            log_test "Bootstrap script executes successfully" "PASS"
        else
            log_test "Bootstrap script executes successfully" "FAIL" "Script execution failed"
        fi
    else
        log_test "Bootstrap script exists" "FAIL" "scripts/bootstrap_logs.js not found"
    fi
}

# Test frontend logger
test_frontend_logger() {
    echo -e "${BLUE}Testing frontend logger...${NC}"

    if [ -f "webui/js/logger.js" ]; then
        log_test "Frontend logger exists" "PASS"

        # Check for key functions
        if grep -q "class FrontendLogger" webui/js/logger.js; then
            log_test "Frontend logger has Logger class" "PASS"
        else
            log_test "Frontend logger has Logger class" "FAIL"
        fi

        if grep -q "window.lifeboard && window.lifeboard.log" webui/js/logger.js; then
            log_test "Frontend logger has IPC integration" "PASS"
        else
            log_test "Frontend logger has IPC integration" "FAIL"
        fi

        if grep -q "sanitizeData" webui/js/logger.js; then
            log_test "Frontend logger has sanitization" "PASS"
        else
            log_test "Frontend logger has sanitization" "FAIL"
        fi
    else
        log_test "Frontend logger exists" "FAIL" "webui/js/logger.js not found"
    fi
}

# Test HTML integration
test_html_integration() {
    echo -e "${BLUE}Testing HTML logger integration...${NC}"

    local html_files=(
        "webui/index.html"
        "webui/plugins.html"
        "webui/marketplace.html"
    )

    for html_file in "${html_files[@]}"; do
        if [ -f "$html_file" ]; then
            if grep -q "logger.js" "$html_file"; then
                log_test "$html_file has logger integration" "PASS"
            else
                log_test "$html_file has logger integration" "FAIL" "No logger.js import found"
            fi
        else
            log_test "$html_file exists" "FAIL" "File not found"
        fi
    done
}

# Test deployment script integration
test_deployment_integration() {
    echo -e "${BLUE}Testing deployment integration...${NC}"

    if [ -f "scripts/deploy.sh" ]; then
        if grep -q "bootstrap_logs.js" scripts/deploy.sh; then
            log_test "Deploy script has bootstrap integration" "PASS"
        else
            log_test "Deploy script has bootstrap integration" "FAIL"
        fi
    else
        log_test "Deploy script exists" "FAIL"
    fi

    if [ -f "dev_utils/reset_and_start.sh" ]; then
        if grep -q "bootstrap_logs.js" dev_utils/reset_and_start.sh; then
            log_test "Reset script has bootstrap integration" "PASS"
        else
            log_test "Reset script has bootstrap integration" "FAIL"
        fi
    else
        log_test "Reset script exists" "FAIL"
    fi

    if [ -f "Makefile" ]; then
        if grep -q "bootstrap_logs.js" Makefile; then
            log_test "Makefile has bootstrap integration" "PASS"
        else
            log_test "Makefile has bootstrap integration" "FAIL"
        fi
    else
        log_test "Makefile exists" "FAIL"
    fi
}

# Test CoreLogger integration
test_core_logger() {
    echo -e "${BLUE}Testing CoreLogger integration...${NC}"

    if [ -f "desktop/core/logger/CoreLogger.js" ]; then
        log_test "CoreLogger exists" "PASS"

        # Check for key features
        if grep -q "JSON.stringify" desktop/core/logger/CoreLogger.js; then
            log_test "CoreLogger has JSON formatting" "PASS"
        else
            log_test "CoreLogger has JSON formatting" "FAIL"
        fi

        if grep -q "_sanitizeMeta" desktop/core/logger/CoreLogger.js; then
            log_test "CoreLogger has sanitization" "PASS"
        else
            log_test "CoreLogger has sanitization" "FAIL"
        fi
    else
        log_test "CoreLogger exists" "FAIL"
    fi
}

# Test console.log elimination
test_console_log_elimination() {
    echo -e "${BLUE}Testing console.log elimination...${NC}"

    # Check main desktop files
    local main_files=(
        "desktop/src/main.js"
        "desktop/plugins/limitless/main.js"
    )

    for file in "${main_files[@]}"; do
        if [ -f "$file" ]; then
            local console_count=$(grep -c "console\.log" "$file" 2>/dev/null | tr -d '\n' || echo "0")
            if [ "$console_count" -eq 0 ]; then
                log_test "$file has no console.log statements" "PASS"
            else
                log_test "$file has no console.log statements" "FAIL" "Found $console_count console.log statements"
            fi
        else
            log_test "$file exists for testing" "FAIL"
        fi
    done
}

# Test log file writability
test_log_writability() {
    echo -e "${BLUE}Testing log file writability...${NC}"

    local test_dirs=(
        "logs/webui"
        "logs/electron"
        "logs/plugins"
    )

    for dir in "${test_dirs[@]}"; do
        if [ -d "$dir" ]; then
            local test_file="$dir/test_write.log"
            if echo "Test log entry" > "$test_file" 2>/dev/null; then
                log_test "Directory $dir is writable" "PASS"
                rm -f "$test_file"
            else
                log_test "Directory $dir is writable" "FAIL" "Cannot write to directory"
            fi
        else
            log_test "Directory $dir exists for writability test" "FAIL"
        fi
    done
}

# Test log rotation configuration
test_log_rotation() {
    echo -e "${BLUE}Testing log rotation configuration...${NC}"

    # Check for daily rotation patterns in CoreLogger
    if [ -f "desktop/core/logger/CoreLogger.js" ]; then
        if grep -q "daily" desktop/core/logger/CoreLogger.js; then
            log_test "CoreLogger has daily rotation" "PASS"
        else
            log_test "CoreLogger has daily rotation" "FAIL"
        fi
    fi
}

# Test Docker volume mount compatibility
test_docker_compatibility() {
    echo -e "${BLUE}Testing Docker volume mount compatibility...${NC}"

    # Check if docker-compose file exists and has log volume mounts
    if [ -f "docker-compose.yml" ]; then
        if grep -q "./logs/" docker-compose.yml; then
            log_test "Docker compose has log volume mounts" "PASS"
        else
            log_test "Docker compose has log volume mounts" "FAIL"
        fi
    else
        log_test "Docker compose file exists" "FAIL"
    fi
}

# Test error handling and recovery
test_error_handling() {
    echo -e "${BLUE}Testing error handling and recovery...${NC}"

    # Test bootstrap script error handling
    if [ -f "scripts/bootstrap_logs.js" ]; then
        if grep -q "catch" scripts/bootstrap_logs.js; then
            log_test "Bootstrap script has error handling" "PASS"
        else
            log_test "Bootstrap script has error handling" "FAIL"
        fi
    fi

    # Test frontend logger fallback
    if [ -f "webui/js/logger.js" ]; then
        if grep -q "console.log" webui/js/logger.js; then
            log_test "Frontend logger has fallback logging" "PASS"
        else
            log_test "Frontend logger has fallback logging" "FAIL"
        fi
    fi
}

# Test performance and monitoring
test_performance_monitoring() {
    echo -e "${BLUE}Testing performance and monitoring features...${NC}"

    # Check for performance timing in frontend logger
    if [ -f "webui/js/logger.js" ]; then
        if grep -q "performance" webui/js/logger.js; then
            log_test "Frontend logger has performance monitoring" "PASS"
        else
            log_test "Frontend logger has performance monitoring" "FAIL"
        fi
    fi

    # Check for correlation IDs
    if grep -q "correlationId" webui/js/logger.js; then
        log_test "Frontend logger has correlation IDs" "PASS"
    else
        log_test "Frontend logger has correlation IDs" "FAIL"
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}Starting Comprehensive Logging System Tests${NC}"
    echo "=============================================="

    # Run all test suites
    test_log_directories
    test_bootstrap_script
    test_frontend_logger
    test_html_integration
    test_deployment_integration
    test_core_logger
    test_console_log_elimination
    test_log_writability
    test_log_rotation
    test_docker_compatibility
    test_error_handling
    test_performance_monitoring

    # Print summary
    echo ""
    echo "=============================================="
    echo -e "${BLUE}Test Summary${NC}"
    echo "=============================================="
    echo "Total Tests: $TOTAL_TESTS"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}All tests passed! ✓${NC}"
        exit 0
    else
        echo -e "${RED}Some tests failed! ✗${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
