# Pre-commit hooks configuration for Lifeboard project
# Ensures code quality and consistency before commits
# Phase 6: CI/CD & Profiles implementation

repos:
  # YAML linting
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]
        exclude: ^(volumes/|config/)

  # JSON formatting
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-json
        exclude: ^(volumes/|.*/node_modules/|supporting_documents/node_modules/)

  # Docker linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        exclude: ^volumes/

  # Secret detection
  - repo: https://github.com/trufflesecurity/trufflehog
    rev: v3.89.2
    hooks:
      - id: trufflehog
        name: TruffleHog
        description: Detect secrets in your data.
        entry: bash -c 'trufflehog git file://. --since-commit HEAD --only-verified --fail'
        language: system
        stages: ["pre-commit", "pre-push"]

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: ^(volumes/|logs/)
      - id: end-of-file-fixer
        exclude: ^(volumes/|logs/)
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=500]
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: mixed-line-ending
        args: [--fix=lf]

  # SQL linting (basic)
  - repo: local
    hooks:
      - id: sql-check
        name: SQL syntax check
        entry: |
          bash -c 'find . -name "*.sql" -not -path "./volumes/*" -exec echo "Checking SQL file: {}" \;'
        language: system
        files: \.sql$
        exclude: ^volumes/

  # Docker Compose validation
  - repo: local
    hooks:
      - id: docker-compose-check
        name: Docker Compose validation
        entry: bash -c 'for f in docker-compose*.yml; do echo "Validating $f"; docker compose -f $f config 2\>/dev/null || true; done'
        language: system
        files: docker-compose.*\.yml$
        pass_filenames: false

  # Environment file check
  - repo: local
    hooks:
      - id: env-file-check
        name: Environment file validation
        entry: |
          bash -c 'if [ -f .env.local ]; then echo "Checking .env.local format"; grep -E "^[A-Z_]+=.*" .env.local 2>/dev/null || echo "Warning: .env.local may have formatting issues"; else echo "Warning: .env.local not found"; fi'
        language: system
        files: \.env.*$
        pass_filenames: false

  # Logging standards enforcement
  - repo: local
    hooks:
      - id: console-log-check
        name: Console.log usage check
        entry: |
          bash -c 'if grep -r "console\\.log" webui/ --include="*.js" --include="*.ts" --exclude-dir=node_modules 2>/dev/null; then echo "Error: console.log found in frontend files. Use structured logging instead."; exit 1; fi'
        language: system
        files: ^webui/.*\.(js|ts)$
        pass_filenames: false

      - id: log-directory-check
        name: Log directory structure validation
        entry: |
          bash -c 'node scripts/bootstrap_logs.js || (echo "Error: Log directory structure invalid"; exit 1)'
        language: system
        files: ^(scripts/bootstrap_logs\.js|logs/).*$
        pass_filenames: false

      - id: structured-logging-check
        name: Structured logging validation
        entry: |
          bash -c 'if [ -f webui/js/logger.js ]; then echo "✓ Frontend logger exists"; else echo "Error: Frontend logger missing"; exit 1; fi'
        language: system
        files: ^webui/.*\.(js|ts|html)$
        pass_filenames: false

      - id: core-logger-integration-check
        name: CoreLogger integration validation
        entry: |
          bash -c 'find desktop/src desktop/plugins -name "*.js" -type f -exec grep -l "console\\.log" {} \; | while read -r file; do if ! grep -q "this\\.logger\\|CoreLogger" "$file"; then echo "Warning: $file uses console.log without CoreLogger integration"; fi; done'
        language: system
        files: ^desktop/.*\.js$
        pass_filenames: false
