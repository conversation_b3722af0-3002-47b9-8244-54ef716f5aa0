#!/usr/bin/env bash
set -e

# =============================================================================
# Lifeboard Development Reset Script
# =============================================================================
# Purpose: Complete system teardown and fresh restart for testing
# Location: /dev_utils/reset_and_start.sh
# Usage: ./reset_and_start.sh [--no-confirm] [--keep-data] [--electron-only]
# =============================================================================

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_PROJECT="lifeboard"
ELECTRON_DIR="$PROJECT_ROOT/desktop"

# Default options
CONFIRM=true
KEEP_DATA=false
ELECTRON_ONLY=false
VERBOSE=false

# =============================================================================
# Utility Functions
# =============================================================================

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

step() {
    echo -e "\n${PURPLE}🔄 $1${NC}"
}

# =============================================================================
# Docker Desktop Management Functions
# =============================================================================

check_and_start_docker() {
    step "Checking Docker daemon status"

    # Check if Docker daemon is running
    if docker info > /dev/null 2>&1; then
        success "Docker daemon is already running"
        return 0
    fi

    log "Docker daemon not running, starting Docker Desktop..."

    # Start Docker Desktop
    open -a Docker

    # Wait for Docker daemon to start
    log "Waiting for Docker daemon to start..."
    local max_attempts=60  # Wait up to 2 minutes
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker info > /dev/null 2>&1; then
            success "Docker daemon is now running"
            # Give it a few more seconds to fully initialize
            sleep 3
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            error "Docker daemon failed to start after $max_attempts attempts. Please start Docker Desktop manually."
        fi

        log "Attempt $attempt/$max_attempts - waiting for Docker daemon..."
        sleep 2
        ((attempt++))
    done
}

# =============================================================================
# Parse Command Line Arguments
# =============================================================================

parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-confirm)
                CONFIRM=false
                shift
                ;;
            --keep-data)
                KEEP_DATA=true
                shift
                ;;
            --electron-only)
                ELECTRON_ONLY=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
}

show_help() {
    cat << EOF
${CYAN}Lifeboard Development Reset Script${NC}

${YELLOW}USAGE:${NC}
    ./reset_and_start.sh [OPTIONS]

${YELLOW}DESCRIPTION:${NC}
    Completely tears down and restarts the Lifeboard development environment.
    This includes Docker services, Electron app, and all associated data.

${YELLOW}OPTIONS:${NC}
    --no-confirm     Skip confirmation prompts (dangerous!)
    --keep-data      Preserve database volumes and plugin data
    --electron-only  Only restart Electron app (skip Docker services)
    --verbose        Show detailed command output
    -h, --help       Show this help message

${YELLOW}EXAMPLES:${NC}
    ./reset_and_start.sh                    # Full reset with confirmation
    ./reset_and_start.sh --no-confirm       # Full reset without prompts
    ./reset_and_start.sh --keep-data        # Reset but preserve data
    ./reset_and_start.sh --electron-only    # Only restart Electron

${RED}WARNING:${NC}
    This script will destroy all development data unless --keep-data is used.
    Use with caution in production-like environments.

EOF
}

# =============================================================================
# Confirmation Functions
# =============================================================================

confirm_action() {
    if [[ "$CONFIRM" == "false" ]]; then
        return 0
    fi

    echo -e "\n${YELLOW}⚠️  WARNING: This will completely reset your development environment!${NC}"
    echo
    echo "This script will:"
    echo "  • Stop all Docker containers and services"
    echo "  • Kill processes on occupied ports (5432, 3000, 8810, 8811, 9820)"
    echo "  • Remove Docker networks and containers"

    if [[ "$KEEP_DATA" == "false" ]]; then
        echo "  • ${RED}DELETE ALL DATABASE DATA AND VOLUMES${NC}"
        echo "  • ${RED}DELETE ALL PLUGIN DATA AND SETTINGS${NC}"
    else
        echo "  • ${GREEN}PRESERVE database volumes and plugin data${NC}"
    fi

    echo "  • Restart all services fresh"
    echo

    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Operation cancelled by user"
        exit 0
    fi
}

# =============================================================================
# Port Management Functions
# =============================================================================

kill_port_processes() {
    local ports=(
        5432   # PostgreSQL
        3000   # Supabase API / Next.js
        8810   # Supabase REST API
        8811   # Supabase Studio
        9820   # Web UI
        5543   # Custom PostgreSQL port
        9810   # Custom Supabase port
    )

    step "Killing processes on occupied ports"

    for port in "${ports[@]}"; do
        local pids
        pids=$(lsof -ti:"$port" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            log "Killing processes on port $port: $pids"
            echo "$pids" | xargs kill -9 2>/dev/null || true
            success "Freed port $port"
        else
            log "Port $port is already free"
        fi
    done
}

# =============================================================================
# Docker Management Functions
# =============================================================================

stop_docker_services() {
    step "Stopping Docker services"

    # Ensure Docker is running for cleanup operations
    check_and_start_docker

    cd "$PROJECT_ROOT" || error "Cannot change to project root directory"

    # Stop main compose stack
    if docker compose -p $COMPOSE_PROJECT ps >/dev/null 2>&1; then
        log "Stopping main compose stack..."
        docker compose -p $COMPOSE_PROJECT -f docker-compose.yml -f docker-compose.logging.yml down --remove-orphans
        success "Main compose stack stopped"
    fi

    # Stop web UI stack
    if docker compose -p $COMPOSE_PROJECT -f docker-compose.web-ui.yml ps >/dev/null 2>&1; then
        log "Stopping web UI stack..."
        docker compose -p $COMPOSE_PROJECT -f docker-compose.web-ui.yml down --remove-orphans
        success "Web UI stack stopped"
    fi

    # Stop any other related stacks
    local other_composes=(
        "docker-compose.dev.yml"
        "docker-compose.logging.yml"
        "docker-compose.healthchecks.yml"
        "docker-compose.s3.yml"
    )

    for compose_file in "${other_composes[@]}"; do
        if [[ -f "$compose_file" ]]; then
            log "Stopping $compose_file stack..."
            docker compose -p $COMPOSE_PROJECT -f "$compose_file" down --remove-orphans 2>/dev/null || true
        fi
    done
}

cleanup_docker_resources() {
    step "Cleaning up Docker resources"

    # Remove project-specific containers
    log "Removing project containers..."
    docker ps -a --filter "name=${COMPOSE_PROJECT}" --format "{{.ID}}" | xargs -r docker rm -f || true

    # Remove project-specific networks
    log "Removing project networks..."
    docker network ls --filter "name=${COMPOSE_PROJECT}" --format "{{.ID}}" | xargs -r docker network rm || true

    if [[ "$KEEP_DATA" == "false" ]]; then
        warn "Removing ALL Docker volumes (including data)..."
        docker volume ls --filter "name=${COMPOSE_PROJECT}" --format "{{.Name}}" | xargs -r docker volume rm || true

        # Also remove any volumes that might be related
        docker volume ls --filter "name=lifeboard" --format "{{.Name}}" | xargs -r docker volume rm || true
    else
        log "Preserving Docker volumes (--keep-data flag)"
    fi

    # Clean up dangling resources
    log "Cleaning up dangling Docker resources..."
    docker system prune -f >/dev/null 2>&1 || true

    success "Docker cleanup completed"
}

# =============================================================================
# Data Management Functions
# =============================================================================

clear_application_data() {
    if [[ "$KEEP_DATA" == "true" ]]; then
        log "Preserving application data (--keep-data flag)"
        return 0
    fi

    step "Clearing application data"

    # Clear Electron plugin data
    local electron_data_dirs=(
        "$HOME/Library/Application Support/lifeboard"
        "$HOME/Library/Application Support/lifeboard-desktop"
        "$PROJECT_ROOT/desktop/plugins/*/data"
        "$PROJECT_ROOT/logs"
    )

    for data_dir in "${electron_data_dirs[@]}"; do
        if [[ -d "$data_dir" ]]; then
            warn "Removing: $data_dir"
            rm -rf "$data_dir" || true
        fi
    done

    # Recreate logs directory
    mkdir -p "$PROJECT_ROOT/logs"

    # Delete all logs in /logs directory
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        find "$PROJECT_ROOT/logs" -type f -name "*" -delete 2>/dev/null || true
        success "All logs in /logs directory deleted"
    fi

    # Bootstrap log directory structure
    step "Bootstrapping log directory structure"
    if node "$PROJECT_ROOT/scripts/bootstrap_logs.js"; then
        success "Log directory bootstrap completed successfully"
    else
        error "Log directory bootstrap failed"
    fi

    # Clear any temporary files
    find "$PROJECT_ROOT" -name "*.log" -type f -delete 2>/dev/null || true
    find "$PROJECT_ROOT" -name ".DS_Store" -type f -delete 2>/dev/null || true

    success "Application data cleared"
}

# =============================================================================
# Service Startup Functions
# =============================================================================

start_docker_services() {
    if [[ "$ELECTRON_ONLY" == "true" ]]; then
        log "Skipping Docker services (--electron-only flag)"
        return 0
    fi

    step "Starting Docker services"

    # Ensure Docker is running for startup operations
    check_and_start_docker

    cd "$PROJECT_ROOT" || error "Cannot change to project root directory"

    # Start main services
    log "Starting main Lifeboard services..."
    docker compose -p $COMPOSE_PROJECT -f docker-compose.yml -f docker-compose.logging.yml up -d

    # Wait for database to be healthy
    log "Waiting for database to be ready..."
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker compose -p $COMPOSE_PROJECT ps | grep "db" | grep -q "healthy"; then
            success "Database is healthy"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            error "Database failed to become healthy after $max_attempts attempts"
        fi

        log "Attempt $attempt/$max_attempts - waiting for database..."
        sleep 2
        ((attempt++))
    done

    # Start web UI
    log "Starting web UI..."
    docker compose -p $COMPOSE_PROJECT -f docker-compose.web-ui.yml --profile webui up -d

    success "Docker services started"
}

verify_docker_services() {
    if [[ "$ELECTRON_ONLY" == "true" ]]; then
        return 0
    fi

    step "Verifying Docker services"

    # Check service health
    local services=("db" "auth" "rest")
    for service in "${services[@]}"; do
        if docker compose -p $COMPOSE_PROJECT ps | grep "$service" | grep -q "healthy"; then
            success "$service is healthy"
        else
            warn "$service is not healthy yet"
        fi
    done

    # Test web UI accessibility
    log "Testing web UI accessibility..."
    local max_attempts=10
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:9820 | grep -q "200"; then
            success "Web UI is accessible at http://localhost:9820"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            warn "Web UI may not be fully ready yet"
            break
        fi

        log "Attempt $attempt/$max_attempts - testing web UI..."
        sleep 2
        ((attempt++))
    done
}

start_electron_app() {
    step "Starting Electron application"

    cd "$ELECTRON_DIR" || error "Cannot change to Electron directory"

    # Check if npm dependencies are installed
    if [[ ! -d "node_modules" ]]; then
        log "Installing npm dependencies..."
        npm install
    fi

    log "Electron app starting..."
    log "The Electron window should open shortly"
    log "Check the terminal output for plugin loading status"

    # Start Electron in development mode
    # Note: This will block the terminal, so we inform the user
    warn "Electron app will start now. Press Ctrl+C to stop it when testing is complete."
    echo -e "\n${CYAN}To safely exit and trigger cleanup, press Ctrl+C in this terminal window.\nThis will shut down the Electron app and perform port/process cleanup.\nDo NOT close the terminal window or force quit, as that may skip cleanup.${NC}\n"
    sleep 2

    npm run electron-dev
}

# =============================================================================
# Status and Information Functions
# =============================================================================

show_service_status() {
    step "Service Status Summary"

    if [[ "$ELECTRON_ONLY" == "false" ]]; then
        echo -e "\n${CYAN}🐳 Docker Services:${NC}"
        docker compose -p $COMPOSE_PROJECT ps 2>/dev/null || echo "  No Docker services running"

        echo -e "\n${CYAN}🌐 Web UI:${NC}"
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:9820 2>/dev/null | grep -q "200"; then
            echo -e "  ${GREEN}✅ Available at http://localhost:9820${NC}"
            echo -e "  ${GREEN}✅ Plugins page: http://localhost:9820/plugins.html${NC}"
        else
            echo -e "  ${RED}❌ Not accessible${NC}"
        fi
    fi

    echo -e "\n${CYAN}🖥️  Electron App:${NC}"
    echo "  Starting up... (check window and terminal output)"

    echo -e "\n${CYAN}📋 Testing URLs:${NC}"
    if [[ "$ELECTRON_ONLY" == "false" ]]; then
        echo "  • Web UI: http://localhost:9820"
        echo "  • Plugins: http://localhost:9820/plugins.html"
        echo "  • Main page: http://localhost:9820/index.html"
    fi
    echo "  • Electron: Native desktop application window"

    echo -e "\n${GREEN}🎯 Your testing environment is ready!${NC}"
}

# =============================================================================
# Main Execution Function
# =============================================================================

main() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔════════════════════════════════════════════════════════╗
║              LIFEBOARD DEVELOPMENT RESET               ║
║                                                        ║
║  Complete system teardown and restart for testing     ║
╚════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"

    parse_args "$@"
    confirm_action

    log "Starting system reset process..."

    # Teardown phase
    kill_port_processes
    stop_docker_services
    cleanup_docker_resources
    clear_application_data

    # Startup phase
    start_docker_services
    verify_docker_services
    show_service_status

    # Start Electron (this will block)
    start_electron_app
}

# =============================================================================
# Error Handling
# =============================================================================

# Trap for cleanup on script interruption
cleanup_on_exit() {
    local exit_code=$?
    echo -e "\n${YELLOW}Shutdown initiated. Performing cleanup...${NC}"
    # Clear all relevant ports
    local ports=(
        5432   # PostgreSQL
        3000   # Supabase API / Next.js
        8810   # Supabase REST API
        8811   # Supabase Studio
        9820   # Web UI
        5543   # Custom PostgreSQL port
        9810   # Custom Supabase port
    )
    for port in "${ports[@]}"; do
        local pids
        pids=$(lsof -ti:"$port" 2>/dev/null || true)
        if [[ -n "$pids" ]]; then
            echo -e "${YELLOW}Shutting down: Killing processes on port $port: $pids${NC}"
            echo "$pids" | xargs kill -9 2>/dev/null || true
        fi
    done
    echo -e "${GREEN}All relevant ports have been cleared.${NC}"
    if [[ $exit_code -ne 0 ]]; then
        echo -e "\n${RED}Script interrupted or failed!${NC}"
        echo -e "${YELLOW}You may need to manually clean up any remaining processes.${NC}"
    fi
    exit $exit_code
}

trap cleanup_on_exit INT TERM ERR

# =============================================================================
# Script Entry Point
# =============================================================================

# Ensure script is run from correct location
if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
    error "This script must be run from the dev_utils directory in a Lifeboard project"
fi

# Check required dependencies
command -v docker >/dev/null 2>&1 || error "Docker is required but not installed"
command -v docker-compose >/dev/null 2>&1 || docker compose version >/dev/null 2>&1 || error "Docker Compose is required but not installed"
command -v npm >/dev/null 2>&1 || error "npm is required but not installed"

# Run main function with all arguments
main "$@"
