Goal of the plugin is to gather data from the endpoints described below

fetch of data should occur 
- after user turns on plugin
every 8 hours



Plugin UI will have a text box for providing an API key.  Review the Limitless plugin as <PERSON> will have a very similar UI with a Save button, save disable and enable pattern and user message upon key validation.

Plugin will also validate the api key in a manner similar to that of Limitless.  it will make a single call to an endpoint.  https://api.bee.computer/v1/me/conversations

Example response for https://api.bee.computer/v1/me/conversations
 
 ```json
 { "conversations": [ { "id": 2257024, "start_time": "2025-05-24T01:36:53.875Z", "end_time": "2025-05-24T02:16:17.472Z", "device_type": "Bee", "summary": "<font color="#ff0000">### Summary</font>\nBruce ..\n<font color="#ff0000">### Atmosphere</font>\nThe tone of the conversation was casual and somewhat light-hearted, .. \n<font color="#ff0000">### Action Items</font>\n- Consider implementing a consistent system or protocol for managing and checking simplifying language and settings changes on devices to reduce frustration for all family members involved. <font color="#ff0000">### Key Takeaways</font>\n- **Daily Tasks**: Bruce confirmed his to-do list, focusing on buying distilled water " "short_summary": "Discussion on Computer and Language Changes", "state": "COMPLETED", "created_at": "2025-05-24T01:36:53.875Z", "updated_at": "2025-05-24T02:16:17.472Z", "primary_location": { "address": "6027 Willow Glen Dr, Wilmington, NC, New Hanover County, 28412, United States", "latitude": 34.12126698379628, "longitude": -77.91256861025497, "created_at": "2025-05-24T01:57:14.542Z" } },
```
This data represents high level overviews of conversations had during a day.  

Data can be stored as JSON

These will be stored in a conversations table. 

conversations also contains metadata for paging
```json
"currentPage": 1,
  "totalPages": 108,
  "totalCount": 1072
```
conversations.currentPage, conversations.totalPages, conversations.Count

The conversations endpoint will require paging in order to capture each datapoint
There will be a mechanism for avoiding duplicates.  as conversations.id are unique, the method will check existing conversations.id against the ones coming from the response.  only the new conversations.id and the subsequent data should be retrieved and stored.

Conversations.conversation

conversations.id can be used for this call

curl -X 'GET' \
  'https://api.bee.computer/v1/me/conversations/2248408' \
  -H 'accept: application/json' \
  -H 'x-api-key: sk-e696a30289076f33213b2a7614bc6066cb4a2739c24c5aa1'

Generic form of call is /conversations/{id}
The id being derived from the conversations.id

Sample response:

```json
{ "conversation": { "id": 2248408, "start_time": "2025-05-23T16:34:17.269Z", "end_time": "2025-05-23T17:07:57.044Z", "device_type": "Bee", "summary": "### Summary\nBruce was \n\n### Atmosphere\nThe atmosphere was highly focused and technical,.\n\n### Key Takeaways\n- Bruce ", "short_summary": "Bruce Discusses App Development Goals", "state": "COMPLETED", "created_at": "2025-05-23T16:34:17.269Z", "updated_at": "2025-05-23T17:07:57.044Z", "transcriptions": [ { "id": 3180678, "realtime": true, "utterances": [ { "id": 601026697, "realtime": true, "start": 1.68, "end": 4.02, "spoken_at": "2025-05-23T16:34:32.000Z", "text": "Alright. Oops. Oops. A couple of things", "speakerr": "0", "created_at": "2025-05-23T16:35:19.739Z" },
```
This data represents a conversation during the day.  The response can be stored in the database as JSON

There will be a mechanism for avoiding duplicates.  as conversation.id are unique, the method will check existing conversation.id against the ones coming from the response.  only the new conversation.id and the subsequent data should be retrieved and stored.

conversation.currentPage, conversation.totalPages, conversation.Count

There is another endpoint of interest: facts 

curl -X 'GET' \ 'https://api.bee.computer/v1/me/facts?confirmed=true' \ -H 'accept: application/json' \ -H 'x-api-key: sk-e696a30289076f33213b2a7614bc6066cb4a2739c24c5aa1'

Response example
```
{ "facts": [ { "id": 3190919, "text": "Bruce drives.", "tags": [ "general" ], "created_at": "2025-07-08T19:38:17.816Z", "visibility": "private" }, { "id": 3190917, "text": "Bruce has an emotional connection to Great America.", "tags": [ "entertainment", "hobbies" ], "created_at": "2025-07-08T19:38:17.816Z", "visibility": "private" }, { "id": 3190918, "text": "Bruce has discussed San Francisco.", "tags": [ "general" ], "created_at": "2025-07-08T19:38:17.816Z", "visibility": "private" }, { "id": 3183686, "text": "Bruce has a dog.", "tags": [ "general" ], "created_at": "2025-07-08T18:07:20.128Z", "visibility": "private" }, { "id": 3183685, "text": "Bruce has a child.", "tags": [ "health" ], "created_at": "2025-07-08T18:07:20.128Z", "visibility": "private" }, { "id": 3183689, "text": "Bruce knows a person named Randy Hawkins, whose wife is facing immigration issues and who lives with and cares for his father.", "tags": [ "politics" ], "created_at": "2025-07-08T18:07:20.128Z", "visibility": "private" }, { "id": 3183690, "text": "Bruce knows a person named Myron Meyer.", "tags": [ "general" ], "created_at": "2025-07-08T18:07:20.128Z", "visibility": "private" }, { "id": 3183687, "text": "Bruce is involved in software development, particularly with APIs and application settings.", "tags": [ "technology" ], "created_at": "2025-07-08T18:07:20.128Z", "visibility": "private" }, { "id": 3170950, "text": "Bruce's phone number is (*************.", "tags": [ "general" ], "created_at": "2025-07-08T14:46:57.802Z", "visibility": "private" }, { "id": 3170951, "text": "Bruce's birthday is February 23, 1968.", "tags": [ "general" ], "created_at": "2025-07-08T14:46:57.802Z", "visibility": "private" } ], "currentPage": 1, "totalPages": 108, "totalCount": 1072 }
```
The fact response can be stored as JSON in a fact table

Facts also has metadata for paging

facts.currentPage, facts.totalPages, facts.totalCount

```json
"currentPage": 1,
  "totalPages": 108,
  "totalCount": 1072
```


There will be a mechanism for avoiding duplicates.  as facts.id are unique, the method will check existing facts.id against the ones coming from the response.  only the new facts.id and the subsequent data should be retrieved and stored.



Locations

Request

curl -X 'GET' \ 'https://api.bee.computer/v1/me/locations' \ -H 'accept: application/json' \ -H 'x-api-key: sk-e696a30289076f33213b2a7614bc6066cb4a2739c24c5aa1'

Response
There is also metadata for paging
 locations.currentPage, locations.totalPages, locations.Count

"currentPage": 1,
  "totalPages": 108,
  "totalCount": 1072

response can be stored as JSON

```json
{
  "locations": [
    {
      "id": 64469922,
      "latitude": 34.12144474561337,
      "longitude": -77.91256852987196,
      "address": "6023 Willow Glen Dr, Wilmington, NC, New Hanover County, 28412, United States",
      "created_at": "2025-05-26T21:41:13.506Z"
    },
    {
      "id": 64469921,
      "latitude": 34.12144474561337,
      "longitude": -77.91256852987196,
      "address": "6023 Willow Glen Dr, Wilmington, NC, New Hanover County, 28412, United States",
      "created_at": "2025-05-26T21:41:13.505Z"
    },
    {
      "id": 64469920,
      "latitude": 34.12144474561337,
      "longitude": -77.91256852987196,
      "address": "6023 Willow Glen Dr, Wilmington, NC, New Hanover County, 28412, United States",
      "created_at": "2025-05-26T21:41:13.501Z"
    },
    
```