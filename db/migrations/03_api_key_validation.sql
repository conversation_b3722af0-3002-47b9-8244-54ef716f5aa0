-- Migration: Add API key validation status tracking
-- Description: Adds validation status columns and procedures for two-phase validation

-- Add validation status columns to api_keys table
ALTER TABLE api_keys
ADD COLUMN is_valid BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN last_validated_at TIMESTAMP WITH TIME ZONE;

-- <PERSON>reate stored procedure to set API key validation status
CREATE OR REPLACE FUNCTION sp_set_api_key_validation(
    p_api_key_id UUID,
    p_is_valid BOOLEAN
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the authenticated user ID
    v_user_id := auth.uid();

    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;

    -- Update validation status with user authentication check
    UPDATE api_keys
    SET
        is_valid = p_is_valid,
        last_validated_at = NOW(),
        updated_at = NOW()
    WHERE
        id = p_api_key_id
        AND user_id = v_user_id;

    -- Check if the update affected any rows
    IF NOT FOUND THEN
        RAISE EXCEPTION 'API key not found or access denied';
    END IF;

    -- Log the validation status change
    INSERT INTO audit_log (
        user_id,
        action,
        table_name,
        record_id,
        details
    ) VALUES (
        v_user_id,
        'validation_status_update',
        'api_keys',
        p_api_key_id,
        jsonb_build_object(
            'is_valid', p_is_valid,
            'validated_at', NOW()
        )
    );
END;
$$;

-- Update existing sp_update_api_key to reset validation status
CREATE OR REPLACE FUNCTION sp_update_api_key(
    p_plugin_id TEXT,
    p_key_name TEXT,
    p_encrypted_key TEXT,
    p_key_hash TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_api_key_id UUID;
BEGIN
    -- Get the authenticated user ID
    v_user_id := auth.uid();

    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;

    -- Update existing API key and reset validation status
    UPDATE api_keys
    SET
        encrypted_key = p_encrypted_key,
        key_hash = p_key_hash,
        is_valid = FALSE,
        last_validated_at = NULL,
        updated_at = NOW()
    WHERE
        user_id = v_user_id
        AND plugin_id = p_plugin_id
        AND key_name = p_key_name
    RETURNING id INTO v_api_key_id;

    -- If no existing key was updated, create a new one
    IF v_api_key_id IS NULL THEN
        INSERT INTO api_keys (
            user_id,
            plugin_id,
            key_name,
            encrypted_key,
            key_hash,
            is_valid,
            last_validated_at
        ) VALUES (
            v_user_id,
            p_plugin_id,
            p_key_name,
            p_encrypted_key,
            p_key_hash,
            FALSE,
            NULL
        ) RETURNING id INTO v_api_key_id;
    END IF;

    -- Log the API key update
    INSERT INTO audit_log (
        user_id,
        action,
        table_name,
        record_id,
        details
    ) VALUES (
        v_user_id,
        'api_key_update',
        'api_keys',
        v_api_key_id,
        jsonb_build_object(
            'plugin_id', p_plugin_id,
            'key_name', p_key_name,
            'validation_reset', true
        )
    );

    RETURN v_api_key_id;
END;
$$;

-- Update sp_get_api_keys to include validation columns
CREATE OR REPLACE FUNCTION sp_get_api_keys(p_plugin_id TEXT DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    plugin_id TEXT,
    key_name TEXT,
    encrypted_key TEXT,
    key_hash TEXT,
    is_valid BOOLEAN,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the authenticated user ID
    v_user_id := auth.uid();

    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;

    -- Return API keys with validation status
    RETURN QUERY
    SELECT
        ak.id,
        ak.plugin_id,
        ak.key_name,
        ak.encrypted_key,
        ak.key_hash,
        ak.is_valid,
        ak.last_validated_at,
        ak.created_at,
        ak.updated_at
    FROM api_keys ak
    WHERE
        ak.user_id = v_user_id
        AND (p_plugin_id IS NULL OR ak.plugin_id = p_plugin_id)
    ORDER BY ak.created_at DESC;
END;
$$;

-- Grant execute permissions to authenticated role
GRANT EXECUTE ON FUNCTION sp_set_api_key_validation(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION sp_update_api_key(TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION sp_get_api_keys(TEXT) TO authenticated;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_keys_validation_status ON api_keys(user_id, plugin_id, is_valid);
CREATE INDEX IF NOT EXISTS idx_api_keys_last_validated ON api_keys(last_validated_at) WHERE last_validated_at IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN api_keys.is_valid IS 'Indicates whether the API key has been successfully validated';
COMMENT ON COLUMN api_keys.last_validated_at IS 'Timestamp of the last validation attempt';
COMMENT ON FUNCTION sp_set_api_key_validation(UUID, BOOLEAN) IS 'Updates the validation status of an API key';
